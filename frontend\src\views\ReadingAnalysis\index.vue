<template>
  <div class="reading-analysis">
    <div class="page-header">
      <h1>阅读分析</h1>
      <p>分析学生阅读行为，提供个性化学习建议</p>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="analysis-card">
          <template #header>
            <span>阅读数据分析</span>
          </template>
          
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="阅读速度" name="speed">
              <div class="chart-container">
                <div id="speedChart" style="height: 300px;"></div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="理解程度" name="comprehension">
              <div class="chart-container">
                <div id="comprehensionChart" style="height: 300px;"></div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="进度追踪" name="progress">
              <div class="progress-content">
                <el-timeline>
                  <el-timeline-item
                    v-for="item in progressData"
                    :key="item.id"
                    :timestamp="item.date"
                    :type="item.type"
                  >
                    <h4>{{ item.title }}</h4>
                    <p>{{ item.description }}</p>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="stats-card">
          <template #header>
            <span>统计概览</span>
          </template>
          
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ stats.totalReadingTime }}</div>
              <div class="stat-label">总阅读时间(分钟)</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-value">{{ stats.averageSpeed }}</div>
              <div class="stat-label">平均阅读速度(字/分)</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-value">{{ stats.comprehensionRate }}%</div>
              <div class="stat-label">理解准确率</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-value">{{ stats.completedTexts }}</div>
              <div class="stat-label">完成文本数</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="recommendations-card">
          <template #header>
            <span>学习建议</span>
          </template>
          
          <div class="recommendations">
            <div v-for="rec in recommendations" :key="rec.id" class="recommendation-item">
              <el-tag :type="rec.type" size="small">{{ rec.category }}</el-tag>
              <p>{{ rec.content }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'ReadingAnalysis',
  data() {
    return {
      activeTab: 'speed',
      stats: {
        totalReadingTime: 245,
        averageSpeed: 180,
        comprehensionRate: 85,
        completedTexts: 12
      },
      progressData: [
        {
          id: 1,
          date: '2024-01-15',
          type: 'success',
          title: '完成《科技文章》阅读',
          description: '阅读速度提升15%，理解准确率达到90%'
        },
        {
          id: 2,
          date: '2024-01-12',
          type: 'primary',
          title: '开始新的阅读计划',
          description: '制定了为期一个月的阅读提升计划'
        },
        {
          id: 3,
          date: '2024-01-10',
          type: 'warning',
          title: '阅读速度下降',
          description: '建议调整阅读策略，增加练习时间'
        }
      ],
      recommendations: [
        {
          id: 1,
          category: '速度提升',
          type: 'primary',
          content: '建议每天进行15分钟的快速阅读练习，逐步提高阅读速度。'
        },
        {
          id: 2,
          category: '理解加强',
          type: 'success',
          content: '可以尝试阅读后总结要点的方法来提高理解能力。'
        },
        {
          id: 3,
          category: '词汇扩展',
          type: 'warning',
          content: '建议增加词汇量练习，这将有助于提高整体阅读水平。'
        }
      ]
    }
  },
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      // 这里应该初始化图表，暂时用注释代替
      // 实际项目中会使用 ECharts 或其他图表库
      console.log('初始化图表')
    }
  }
}
</script>

<style lang="scss" scoped>
.reading-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.analysis-card {
  margin-bottom: 20px;
}

.stats-card, .recommendations-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
  
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-color-primary);
    margin-bottom: 4px;
  }
  
  .stat-label {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

.chart-container {
  padding: 20px 0;
}

.progress-content {
  padding: 20px;
}

.recommendations {
  .recommendation-item {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    p {
      margin: 8px 0 0 0;
      line-height: 1.5;
      color: var(--el-text-color-regular);
    }
  }
}
</style>
