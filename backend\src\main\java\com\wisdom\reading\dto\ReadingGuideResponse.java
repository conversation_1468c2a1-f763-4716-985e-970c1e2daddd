package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 阅读指导响应DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReadingGuideResponse {

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 关联的分析ID
     */
    private Long analysisId;

    /**
     * 阅读指导内容
     */
    private ReadingGuide guide;

    /**
     * 学生水平
     */
    private String studentLevel;

    /**
     * 适用年级
     */
    private String targetGrade;

    /**
     * 生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 预计使用时间（分钟）
     */
    private Integer estimatedTime;

    /**
     * 使用说明
     */
    private String instructions;

    /**
     * 阅读指导内容
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReadingGuide {
        /**
         * 准备步骤
         */
        private List<String> preparationSteps;

        /**
         * 阅读策略
         */
        private List<String> readingStrategies;

        /**
         * 讨论要点
         */
        private List<String> discussionPoints;

        /**
         * 拓展活动
         */
        private List<String> extensionActivities;

        /**
         * 时间估计
         */
        private String timeEstimate;

        /**
         * 重点词汇
         */
        private List<String> keyVocabulary;

        /**
         * 背景知识
         */
        private String backgroundKnowledge;

        /**
         * 阅读目标
         */
        private List<String> readingObjectives;

        /**
         * 评估方式
         */
        private List<String> assessmentMethods;
    }
}
