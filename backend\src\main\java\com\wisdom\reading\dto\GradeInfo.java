package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分级信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GradeInfo {

    /**
     * 分级代码
     */
    private String gradeCode;

    /**
     * 分级名称
     */
    private String gradeName;

    /**
     * 分级描述
     */
    private String gradeDescription;

    /**
     * 置信度
     */
    private Double confidence;

    /**
     * 推荐年级范围
     */
    private String recommendedGrades;

    /**
     * 颜色标识
     */
    private String color;

    /**
     * 最小年龄
     */
    private Integer minAge;

    /**
     * 最大年龄
     */
    private Integer maxAge;

    /**
     * 难度等级 (1-10)
     */
    private Integer difficultyLevel;

    /**
     * 是否适合当前用户
     */
    private Boolean suitable;
}
