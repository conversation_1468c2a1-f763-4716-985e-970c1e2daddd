package com.wisdom.reading.service;

import com.wisdom.reading.dto.TextAnalysisRequest;
import com.wisdom.reading.dto.TextAnalysisResponse;
import com.wisdom.reading.dto.TextSimplificationRequest;
import com.wisdom.reading.dto.TextSimplificationResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * AI分析服务接口
 * 
 * 这是核心的AI功能接口，负责调用AI模型进行文本分析、分级、简化等操作
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
public interface AIAnalysisService {

    /**
     * AI文本分级分析
     * 
     * 调用AI模型对文本进行难度分级和详细分析
     * 
     * 请求参数格式：
     * {
     *   "text": "要分析的文本内容",
     *   "title": "文本标题（可选）",
     *   "source": "文本来源（可选）",
     *   "detailedAnalysis": true,
     *   "generateTeachingAdvice": false
     * }
     * 
     * 返回结果格式：
     * {
     *   "gradeResult": {
     *     "gradeCode": "PRIMARY_HIGH",
     *     "gradeName": "小学高年级",
     *     "gradeDescription": "5-6年级",
     *     "confidence": 0.85,
     *     "recommendedGrades": "五年级-六年级",
     *     "color": "#F56C6C"
     *   },
     *   "basicStats": {
     *     "textLength": 1200,
     *     "wordCount": 800,
     *     "sentenceCount": 45,
     *     "paragraphCount": 8,
     *     "avgSentenceLength": 17.8
     *   },
     *   "vocabularyAnalysis": {
     *     "newWordRate": 0.15,
     *     "newWords": [
     *       {
     *         "word": "蜿蜒",
     *         "partOfSpeech": "动词",
     *         "difficultyLevel": "高级",
     *         "frequency": 2,
     *         "suggestedReplacement": "弯曲"
     *       }
     *     ],
     *     "keywords": ["自然", "山川", "河流"],
     *     "distribution": {
     *       "basicWordsRate": 0.70,
     *       "commonWordsRate": 0.15,
     *       "advancedWordsRate": 0.15
     *     }
     *   },
     *   "sentenceAnalysis": {
     *     "complexSentenceRate": 0.25,
     *     "longSentences": [
     *       {
     *         "content": "这是一个很长的句子...",
     *         "length": 45,
     *         "complexityScore": 0.8,
     *         "simplificationSuggestion": "建议拆分为两个句子"
     *       }
     *     ],
     *     "distribution": {
     *       "simpleSentenceRate": 0.60,
     *       "compoundSentenceRate": 0.15,
     *       "complexSentenceRate": 0.25
     *     }
     *   },
     *   "topicAnalysis": {
     *     "category": "自然",
     *     "topicKeywords": ["山川", "河流", "自然"],
     *     "sentiment": "中性",
     *     "textType": "说明文"
     *   },
     *   "teachingAdvice": {
     *     "teachingFocus": ["生词理解", "长句分析"],
     *     "preparationAdvice": ["预习生词", "了解背景"],
     *     "readingStrategies": ["分段阅读", "关键词标记"],
     *     "practiceAdvice": ["词汇练习", "句式分析"]
     *   }
     * }
     * 
     * @param request 分析请求
     * @return 分析结果
     */
    TextAnalysisResponse analyzeText(TextAnalysisRequest request);

    /**
     * AI文本简化
     * 
     * 调用AI模型将文本简化到指定年级水平
     * 
     * 请求参数格式：
     * {
     *   "text": "要简化的文本内容",
     *   "targetGrade": "PRIMARY_MID",
     *   "analysisId": 123,
     *   "simplificationLevel": "MODERATE",
     *   "preserveStructure": true,
     *   "generateComparison": true,
     *   "specialRequirements": "保持故事情节完整"
     * }
     * 
     * 返回结果格式：
     * {
     *   "originalText": "原始文本内容",
     *   "simplifiedText": "简化后的文本内容",
     *   "targetGrade": "PRIMARY_MID",
     *   "originalGrade": {
     *     "gradeCode": "PRIMARY_HIGH",
     *     "gradeName": "小学高年级",
     *     "confidence": 0.85
     *   },
     *   "simplifiedGrade": {
     *     "gradeCode": "PRIMARY_MID",
     *     "gradeName": "小学中年级",
     *     "confidence": 0.90
     *   },
     *   "statistics": {
     *     "originalWordCount": 800,
     *     "simplifiedWordCount": 650,
     *     "wordCountChangeRate": -0.1875,
     *     "originalSentenceCount": 45,
     *     "simplifiedSentenceCount": 52,
     *     "sentenceCountChangeRate": 0.1556,
     *     "simplificationRatio": 0.75
     *   },
     *   "wordReplacements": [
     *     {
     *       "originalWord": "蜿蜒",
     *       "replacementWord": "弯曲",
     *       "reason": "降低词汇难度",
     *       "positions": [45, 123],
     *       "difficultyReduction": "高级->基础"
     *     }
     *   ],
     *   "sentenceAdjustments": [
     *     {
     *       "originalSentence": "这是一个复杂的长句子...",
     *       "adjustedSentence": "这是第一部分。这是第二部分。",
     *       "adjustmentType": "SPLIT",
     *       "explanation": "将长句拆分为两个简单句",
     *       "position": 3
     *     }
     *   ],
     *   "qualityAssessment": {
     *     "overallScore": 85.5,
     *     "readabilityImprovement": 78.0,
     *     "contentFidelity": 92.0,
     *     "languageFluency": 87.0,
     *     "assessmentNotes": "简化效果良好，保持了原文主要内容",
     *     "improvementSuggestions": ["可以进一步简化部分专业术语"]
     *   }
     * }
     * 
     * @param request 简化请求
     * @return 简化结果
     */
    TextSimplificationResponse simplifyText(TextSimplificationRequest request);

    /**
     * AI文本推荐
     * 
     * 根据用户的阅读水平和兴趣推荐合适的文本
     * 
     * 请求参数格式：
     * {
     *   "userGrade": "PRIMARY_HIGH",
     *   "interests": ["科学", "历史"],
     *   "textType": "说明文",
     *   "difficulty": "MODERATE",
     *   "length": "MEDIUM",
     *   "count": 10
     * }
     * 
     * 返回结果格式：
     * [
     *   {
     *     "id": "text_001",
     *     "title": "恐龙的秘密",
     *     "content": "文本内容...",
     *     "gradeLevel": "PRIMARY_HIGH",
     *     "category": "科学",
     *     "textType": "说明文",
     *     "length": 1200,
     *     "difficulty": 0.75,
     *     "matchScore": 0.92,
     *     "recommendReason": "符合用户年级水平，主题匹配兴趣"
     *   }
     * ]
     * 
     * @param userGrade 用户年级
     * @param interests 兴趣标签
     * @param textType 文本类型
     * @param difficulty 难度要求
     * @param length 长度要求
     * @param count 推荐数量
     * @return 推荐文本列表
     */
    List<RecommendedText> recommendTexts(String userGrade, List<String> interests, 
                                       String textType, String difficulty, 
                                       String length, Integer count);

    /**
     * AI教学资源生成
     * 
     * 根据文本自动生成教学资源
     * 
     * 请求参数格式：
     * {
     *   "text": "文本内容",
     *   "gradeLevel": "PRIMARY_HIGH",
     *   "resourceTypes": ["VOCABULARY_CARDS", "QUESTIONS", "READING_GUIDE"],
     *   "studentLevel": "BEGINNER"
     * }
     * 
     * 返回结果格式：
     * {
     *   "vocabularyCards": [
     *     {
     *       "word": "蜿蜒",
     *       "pronunciation": "wān yán",
     *       "meaning": "弯弯曲曲地延伸",
     *       "example": "小河蜿蜒流过山谷",
     *       "image": "image_url"
     *     }
     *   ],
     *   "comprehensionQuestions": [
     *     {
     *       "type": "MULTIPLE_CHOICE",
     *       "question": "文中提到的主要内容是什么？",
     *       "options": ["A. 选项1", "B. 选项2", "C. 选项3", "D. 选项4"],
     *       "correctAnswer": "B",
     *       "explanation": "答案解释"
     *     }
     *   ],
     *   "readingGuide": {
     *     "preparationSteps": ["预习生词", "了解背景"],
     *     "readingStrategies": ["分段阅读", "关键词标记"],
     *     "discussionPoints": ["讨论要点1", "讨论要点2"]
     *   }
     * }
     * 
     * @param text 文本内容
     * @param gradeLevel 年级水平
     * @param resourceTypes 资源类型
     * @param studentLevel 学生水平
     * @return 教学资源
     */
    TeachingResources generateTeachingResources(String text, String gradeLevel, 
                                              List<String> resourceTypes, String studentLevel);

    /**
     * 推荐文本信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class RecommendedText {
        private String id;
        private String title;
        private String content;
        private String gradeLevel;
        private String category;
        private String textType;
        private Integer length;
        private Double difficulty;
        private Double matchScore;
        private String recommendReason;
    }

    /**
     * 教学资源
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class TeachingResources {
        private List<com.wisdom.reading.dto.VocabularyCard> vocabularyCards;
        private List<com.wisdom.reading.dto.ComprehensionQuestionsResponse.Question> comprehensionQuestions;
        private String readingGuide;
    }
}
