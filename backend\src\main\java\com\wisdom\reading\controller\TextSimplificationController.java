package com.wisdom.reading.controller;

import com.wisdom.reading.dto.QualityAssessment;
import com.wisdom.reading.dto.TextSimplificationRequest;
import com.wisdom.reading.dto.TextSimplificationResponse;
import com.wisdom.reading.service.TextSimplificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 文本简化控制器
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Tag(name = "文本简化", description = "文本简化和改写相关接口")
@RestController
@RequestMapping("/text-simplification")
@RequiredArgsConstructor
public class TextSimplificationController {

    private final TextSimplificationService textSimplificationService;

    @Operation(summary = "文本简化", description = "将文本简化到指定年级水平")
    @PostMapping("/simplify")
    public ResponseEntity<TextSimplificationResponse> simplifyText(
            @Valid @RequestBody TextSimplificationRequest request) {
        
        TextSimplificationResponse response = textSimplificationService.simplifyText(request);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "基于分析结果简化", description = "基于已有的分析结果进行文本简化")
    @PostMapping("/simplify-from-analysis")
    public ResponseEntity<TextSimplificationResponse> simplifyFromAnalysis(
            @Parameter(description = "分析ID") @RequestParam Long analysisId,
            @Parameter(description = "目标年级") @RequestParam String targetGrade,
            @Parameter(description = "简化程度") @RequestParam(defaultValue = "MODERATE") String simplificationLevel) {
        
        TextSimplificationResponse response = textSimplificationService.simplifyFromAnalysis(
                analysisId, targetGrade, simplificationLevel);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "批量文本简化", description = "批量简化多个文本")
    @PostMapping("/batch-simplify")
    public ResponseEntity<List<TextSimplificationResponse>> batchSimplify(
            @Valid @RequestBody List<TextSimplificationRequest> requests) {
        
        List<TextSimplificationResponse> responses = textSimplificationService.batchSimplify(requests);
        return ResponseEntity.ok(responses);
    }

    @Operation(summary = "获取简化历史", description = "获取用户的文本简化历史记录")
    @GetMapping("/history")
    public ResponseEntity<List<TextSimplificationResponse>> getSimplificationHistory(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "目标年级筛选") @RequestParam(required = false) String targetGrade) {
        
        List<TextSimplificationResponse> history = textSimplificationService.getSimplificationHistory(
                page, size, targetGrade);
        return ResponseEntity.ok(history);
    }

    @Operation(summary = "获取简化详情", description = "根据简化ID获取详细简化结果")
    @GetMapping("/{simplificationId}")
    public ResponseEntity<TextSimplificationResponse> getSimplificationDetail(
            @Parameter(description = "简化ID") @PathVariable Long simplificationId) {
        
        TextSimplificationResponse response = textSimplificationService.getSimplificationDetail(simplificationId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "删除简化记录", description = "删除指定的简化记录")
    @DeleteMapping("/{simplificationId}")
    public ResponseEntity<Void> deleteSimplification(
            @Parameter(description = "简化ID") @PathVariable Long simplificationId) {
        
        textSimplificationService.deleteSimplification(simplificationId);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "简化效果评估", description = "评估简化文本的质量和效果")
    @PostMapping("/{simplificationId}/evaluate")
    public ResponseEntity<QualityAssessment> evaluateSimplification(
            @Parameter(description = "简化ID") @PathVariable Long simplificationId) {

        QualityAssessment assessment = textSimplificationService.evaluateSimplification(simplificationId);
        return ResponseEntity.ok(assessment);
    }


}
