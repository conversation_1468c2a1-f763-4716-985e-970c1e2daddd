package com.wisdom.reading.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文本分析响应DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
public class TextAnalysisResponse {

    /**
     * 分析记录ID
     */
    private Long analysisId;

    /**
     * 文本标题
     */
    private String title;

    /**
     * 文本来源
     */
    private String source;

    /**
     * 分级结果
     */
    private GradeResult gradeResult;

    /**
     * 基础统计信息
     */
    private BasicStatistics basicStats;

    /**
     * 词汇分析
     */
    private VocabularyAnalysis vocabularyAnalysis;

    /**
     * 句式分析
     */
    private SentenceAnalysis sentenceAnalysis;

    /**
     * 主题分析
     */
    private TopicAnalysis topicAnalysis;

    /**
     * 教学建议（可选）
     */
    private TeachingAdvice teachingAdvice;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分级结果
     */
    @Data
    public static class GradeResult {
        /**
         * 分级代码
         */
        private String gradeCode;

        /**
         * 分级名称
         */
        private String gradeName;

        /**
         * 分级描述
         */
        private String gradeDescription;

        /**
         * 置信度
         */
        private Double confidence;

        /**
         * 推荐年级范围
         */
        private String recommendedGrades;

        /**
         * 颜色标识
         */
        private String color;
    }

    /**
     * 基础统计信息
     */
    @Data
    public static class BasicStatistics {
        /**
         * 文本长度
         */
        private Integer textLength;

        /**
         * 词汇总数
         */
        private Integer wordCount;

        /**
         * 句子总数
         */
        private Integer sentenceCount;

        /**
         * 段落总数
         */
        private Integer paragraphCount;

        /**
         * 平均句长
         */
        private Double avgSentenceLength;
    }

    /**
     * 词汇分析
     */
    @Data
    public static class VocabularyAnalysis {
        /**
         * 生词率
         */
        private Double newWordRate;

        /**
         * 生词列表
         */
        private List<WordInfo> newWords;

        /**
         * 关键词列表
         */
        private List<String> keywords;

        /**
         * 词汇分布
         */
        private VocabularyDistribution distribution;
    }

    /**
     * 词汇信息
     */
    @Data
    public static class WordInfo {
        /**
         * 词汇
         */
        private String word;

        /**
         * 词性
         */
        private String partOfSpeech;

        /**
         * 难度等级
         */
        private String difficultyLevel;

        /**
         * 出现次数
         */
        private Integer frequency;

        /**
         * 建议替换词
         */
        private String suggestedReplacement;
    }

    /**
     * 词汇分布
     */
    @Data
    public static class VocabularyDistribution {
        /**
         * 基础词汇比例
         */
        private Double basicWordsRate;

        /**
         * 常用词汇比例
         */
        private Double commonWordsRate;

        /**
         * 高级词汇比例
         */
        private Double advancedWordsRate;
    }

    /**
     * 句式分析
     */
    @Data
    public static class SentenceAnalysis {
        /**
         * 复杂句比例
         */
        private Double complexSentenceRate;

        /**
         * 长句列表
         */
        private List<SentenceInfo> longSentences;

        /**
         * 句式分布
         */
        private SentenceDistribution distribution;
    }

    /**
     * 句子信息
     */
    @Data
    public static class SentenceInfo {
        /**
         * 句子内容
         */
        private String content;

        /**
         * 句子长度
         */
        private Integer length;

        /**
         * 复杂度评分
         */
        private Double complexityScore;

        /**
         * 简化建议
         */
        private String simplificationSuggestion;
    }

    /**
     * 句式分布
     */
    @Data
    public static class SentenceDistribution {
        /**
         * 简单句比例
         */
        private Double simpleSentenceRate;

        /**
         * 复合句比例
         */
        private Double compoundSentenceRate;

        /**
         * 复杂句比例
         */
        private Double complexSentenceRate;
    }

    /**
     * 主题分析
     */
    @Data
    public static class TopicAnalysis {
        /**
         * 主题分类
         */
        private String category;

        /**
         * 主题关键词
         */
        private List<String> topicKeywords;

        /**
         * 情感倾向
         */
        private String sentiment;

        /**
         * 文体类型
         */
        private String textType;
    }

    /**
     * 教学建议
     */
    @Data
    public static class TeachingAdvice {
        /**
         * 教学重点
         */
        private List<String> teachingFocus;

        /**
         * 预习建议
         */
        private List<String> preparationAdvice;

        /**
         * 阅读策略
         */
        private List<String> readingStrategies;

        /**
         * 练习建议
         */
        private List<String> practiceAdvice;
    }
}
