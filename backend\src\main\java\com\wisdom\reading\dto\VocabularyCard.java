package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 生词卡片DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VocabularyCard {

    /**
     * 词汇
     */
    private String word;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 词性
     */
    private String partOfSpeech;

    /**
     * 基本释义
     */
    private String basicMeaning;

    /**
     * 详细释义
     */
    private List<String> detailedMeanings;

    /**
     * 例句
     */
    private List<String> examples;

    /**
     * 同义词
     */
    private List<String> synonyms;

    /**
     * 反义词
     */
    private List<String> antonyms;

    /**
     * 词汇难度等级 (1-10)
     */
    private Integer difficultyLevel;

    /**
     * 使用频率：HIGH(高频), MEDIUM(中频), LOW(低频)
     */
    private String frequency;

    /**
     * 在原文中的上下文
     */
    private String context;

    /**
     * 记忆提示
     */
    private String memoryTip;

    /**
     * 相关词汇
     */
    private List<String> relatedWords;

    /**
     * 词汇来源
     */
    private String source;

    /**
     * 是否为重点词汇
     */
    private Boolean isKeyWord;

    /**
     * 学习建议
     */
    private String learningTip;
}
