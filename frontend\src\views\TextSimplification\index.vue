<template>
  <div class="text-simplification">
    <div class="page-header">
      <h1>文本简化</h1>
      <p>AI智能简化复杂文本，降低阅读难度</p>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="input-card">
          <template #header>
            <span>原始文本</span>
          </template>
          
          <el-form :model="form" label-width="80px">
            <el-form-item label="文本内容">
              <el-input
                v-model="form.originalText"
                type="textarea"
                :rows="12"
                placeholder="请输入要简化的文本内容..."
                maxlength="3000"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="简化程度">
              <el-radio-group v-model="form.simplificationLevel">
                <el-radio label="light">轻度简化</el-radio>
                <el-radio label="moderate">中度简化</el-radio>
                <el-radio label="heavy">重度简化</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="simplifyText" :loading="simplifying">
                开始简化
              </el-button>
              <el-button @click="clearText">清空</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="result-card">
          <template #header>
            <div class="card-header">
              <span>简化结果</span>
              <el-button v-if="simplifiedText" type="success" size="small" @click="copyResult">
                复制结果
              </el-button>
            </div>
          </template>
          
          <div v-if="simplifying" class="loading-container">
            <el-skeleton :rows="8" animated />
          </div>
          
          <div v-else-if="simplifiedText" class="result-content">
            <div class="simplified-text">
              {{ simplifiedText }}
            </div>
            
            <el-divider />
            
            <div class="comparison-stats">
              <h4>简化统计</h4>
              <el-descriptions :column="2" size="small">
                <el-descriptions-item label="原文词数">{{ originalStats.wordCount }}</el-descriptions-item>
                <el-descriptions-item label="简化后词数">{{ simplifiedStats.wordCount }}</el-descriptions-item>
                <el-descriptions-item label="词数减少">{{ reductionPercentage }}%</el-descriptions-item>
                <el-descriptions-item label="难度降低">{{ difficultyReduction }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
          
          <div v-else class="empty-state">
            <el-empty description="请输入文本并点击简化按钮" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'TextSimplification',
  data() {
    return {
      simplifying: false,
      form: {
        originalText: '',
        simplificationLevel: 'moderate'
      },
      simplifiedText: '',
      originalStats: {
        wordCount: 0
      },
      simplifiedStats: {
        wordCount: 0
      }
    }
  },
  computed: {
    reductionPercentage() {
      if (this.originalStats.wordCount === 0) return 0
      return Math.round(((this.originalStats.wordCount - this.simplifiedStats.wordCount) / this.originalStats.wordCount) * 100)
    },
    difficultyReduction() {
      const levels = {
        light: '1级',
        moderate: '2级',
        heavy: '3级'
      }
      return levels[this.form.simplificationLevel] || '未知'
    }
  },
  methods: {
    async simplifyText() {
      if (!this.form.originalText.trim()) {
        this.$message.warning('请输入要简化的文本内容')
        return
      }
      
      this.simplifying = true
      this.originalStats.wordCount = this.form.originalText.split(/\s+/).length
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        // 模拟简化结果
        this.simplifiedText = this.generateSimplifiedText(this.form.originalText, this.form.simplificationLevel)
        this.simplifiedStats.wordCount = this.simplifiedText.split(/\s+/).length
        
        this.$message.success('文本简化完成')
      } catch (error) {
        this.$message.error('简化失败，请重试')
      } finally {
        this.simplifying = false
      }
    },
    
    generateSimplifiedText(text, level) {
      // 这里是模拟的简化逻辑，实际应该调用后端API
      const reductionRates = {
        light: 0.9,
        moderate: 0.7,
        heavy: 0.5
      }
      
      const words = text.split(/\s+/)
      const targetLength = Math.floor(words.length * reductionRates[level])
      
      return words.slice(0, targetLength).join(' ') + '...'
    },
    
    clearText() {
      this.form.originalText = ''
      this.simplifiedText = ''
      this.originalStats.wordCount = 0
      this.simplifiedStats.wordCount = 0
    },
    
    async copyResult() {
      try {
        await navigator.clipboard.writeText(this.simplifiedText)
        this.$message.success('结果已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-simplification {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.input-card, .result-card {
  height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px 0;
}

.result-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.simplified-text {
  flex: 1;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  line-height: 1.6;
  overflow-y: auto;
}

.comparison-stats {
  margin-top: 16px;
  
  h4 {
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
  }
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
