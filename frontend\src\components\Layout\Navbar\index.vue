<template>
  <div class="navbar">
    <div class="navbar-left">
      <!-- 侧边栏切换按钮 -->
      <el-button 
        class="sidebar-toggle" 
        type="text" 
        @click="toggleSideBar"
      >
        <el-icon size="20">
          <Expand v-if="!sidebar.opened" />
          <Fold v-else />
        </el-icon>
      </el-button>
      
      <!-- 面包屑导航 -->
      <breadcrumb class="breadcrumb-container" />
    </div>
    
    <div class="navbar-right">
      <!-- 主题切换 -->
      <el-tooltip content="切换主题" placement="bottom">
        <el-button 
          class="theme-toggle" 
          type="text" 
          @click="toggleTheme"
        >
          <el-icon size="18">
            <Sunny v-if="theme === 'light'" />
            <Moon v-else />
          </el-icon>
        </el-button>
      </el-tooltip>
      
      <!-- 用户信息 -->
      <el-dropdown class="user-dropdown" @command="handleCommand">
        <span class="user-info">
          <el-avatar 
            :size="32" 
            :src="userInfo?.avatar" 
            :icon="UserFilled"
          />
          <span class="username">{{ userInfo?.realName || userInfo?.username || '游客' }}</span>
          <el-icon><ArrowDown /></el-icon>
        </span>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Expand, 
  Fold, 
  Sunny, 
  Moon, 
  UserFilled, 
  ArrowDown, 
  User, 
  Setting, 
  SwitchButton 
} from '@element-plus/icons-vue'
import Breadcrumb from './Breadcrumb.vue'

export default {
  name: 'Navbar',
  components: {
    Expand,
    Fold,
    Sunny,
    Moon,
    UserFilled,
    ArrowDown,
    User,
    Setting,
    SwitchButton,
    Breadcrumb
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const sidebar = computed(() => store.getters['app/sidebar'])
    const theme = computed(() => store.getters['app/theme'])
    const userInfo = computed(() => store.getters['user/userInfo'])
    
    const toggleSideBar = () => {
      store.dispatch('app/toggleSideBar')
    }
    
    const toggleTheme = () => {
      const newTheme = theme.value === 'light' ? 'dark' : 'light'
      store.dispatch('app/setTheme', newTheme)
    }
    
    const handleCommand = async (command) => {
      switch (command) {
        case 'profile':
          router.push('/user-center')
          break
        case 'settings':
          ElMessage.info('系统设置功能开发中...')
          break
        case 'logout':
          try {
            await ElMessageBox.confirm(
              '确定要退出登录吗？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
            
            await store.dispatch('user/logout')
            ElMessage.success('退出登录成功')
            router.push('/login')
          } catch (error) {
            if (error !== 'cancel') {
              ElMessage.error('退出登录失败')
            }
          }
          break
      }
    }
    
    return {
      sidebar,
      theme,
      userInfo,
      toggleSideBar,
      toggleTheme,
      handleCommand
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  margin-right: 16px;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.025);
  }
}

.breadcrumb-container {
  margin-left: 8px;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.theme-toggle {
  &:hover {
    background-color: rgba(0, 0, 0, 0.025);
  }
}

.user-dropdown {
  cursor: pointer;
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.025);
    }
    
    .username {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }
}

// 暗色主题
[data-theme="dark"] {
  .navbar {
    background: #1f2937;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  }
  
  .sidebar-toggle:hover,
  .theme-toggle:hover,
  .user-info:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
