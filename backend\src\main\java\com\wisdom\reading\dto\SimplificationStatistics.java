package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 简化统计信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimplificationStatistics {

    /**
     * 原始词汇数
     */
    private Integer originalWordCount;

    /**
     * 简化后词汇数
     */
    private Integer simplifiedWordCount;

    /**
     * 词汇数变化率
     */
    private Double wordCountChangeRate;

    /**
     * 原始句子数
     */
    private Integer originalSentenceCount;

    /**
     * 简化后句子数
     */
    private Integer simplifiedSentenceCount;

    /**
     * 句子数变化率
     */
    private Double sentenceCountChangeRate;

    /**
     * 原始平均句长
     */
    private Double originalAvgSentenceLength;

    /**
     * 简化后平均句长
     */
    private Double simplifiedAvgSentenceLength;

    /**
     * 句长变化率
     */
    private Double sentenceLengthChangeRate;

    /**
     * 词汇替换数量
     */
    private Integer wordReplacementCount;

    /**
     * 句式调整数量
     */
    private Integer sentenceAdjustmentCount;

    /**
     * 简化比例 (0-1)
     */
    private Double simplificationRatio;

    /**
     * 可读性提升度
     */
    private Double readabilityImprovement;

    /**
     * 内容保持度
     */
    private Double contentRetention;

    /**
     * 处理时间（毫秒）
     */
    private Long processingTime;
}
