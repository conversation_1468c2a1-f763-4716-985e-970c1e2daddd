package com.wisdom.reading.controller;

import com.wisdom.reading.dto.LoginRequest;
import com.wisdom.reading.dto.LoginResponse;
import com.wisdom.reading.dto.RegisterRequest;
import com.wisdom.reading.dto.UserInfoResponse;
import com.wisdom.reading.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Tag(name = "用户认证", description = "用户登录、注册、权限管理相关接口")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户登录", description = "用户名密码登录")
    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        LoginResponse response = authService.login(request);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "用户注册", description = "新用户注册")
    @PostMapping("/register")
    public ResponseEntity<Void> register(@Valid @RequestBody RegisterRequest request) {
        authService.register(request);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "用户登出", description = "用户登出")
    @PostMapping("/logout")
    public ResponseEntity<Void> logout() {
        authService.logout();
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "获取用户信息", description = "获取当前登录用户信息")
    @GetMapping("/user-info")
    public ResponseEntity<UserInfoResponse> getUserInfo() {
        UserInfoResponse response = authService.getCurrentUserInfo();
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "刷新Token", description = "刷新访问令牌")
    @PostMapping("/refresh-token")
    public ResponseEntity<LoginResponse> refreshToken(
            @Parameter(description = "刷新令牌") @RequestParam String refreshToken) {
        LoginResponse response = authService.refreshToken(refreshToken);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "修改密码", description = "修改当前用户密码")
    @PostMapping("/change-password")
    public ResponseEntity<Void> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        authService.changePassword(request);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "忘记密码", description = "发送重置密码邮件")
    @PostMapping("/forgot-password")
    public ResponseEntity<Void> forgotPassword(@Valid @RequestBody ForgotPasswordRequest request) {
        authService.forgotPassword(request);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "重置密码", description = "通过重置令牌重置密码")
    @PostMapping("/reset-password")
    public ResponseEntity<Void> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        authService.resetPassword(request);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "发送验证码", description = "发送邮箱或手机验证码")
    @PostMapping("/send-verification-code")
    public ResponseEntity<Void> sendVerificationCode(@Valid @RequestBody SendCodeRequest request) {
        authService.sendVerificationCode(request);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "验证邮箱", description = "验证用户邮箱")
    @PostMapping("/verify-email")
    public ResponseEntity<Void> verifyEmail(@Valid @RequestBody VerifyEmailRequest request) {
        authService.verifyEmail(request);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改密码请求
     */
    @Data
    public static class ChangePasswordRequest {
        private String currentPassword;
        private String newPassword;
    }

    /**
     * 忘记密码请求
     */
    @Data
    public static class ForgotPasswordRequest {
        private String email;
    }

    /**
     * 重置密码请求
     */
    @Data
    public static class ResetPasswordRequest {
        private String token;
        private String newPassword;
    }

    /**
     * 发送验证码请求
     */
    @Data
    public static class SendCodeRequest {
        private String type; // EMAIL, SMS
        private String target; // 邮箱或手机号
    }

    /**
     * 验证邮箱请求
     */
    @Data
    public static class VerifyEmailRequest {
        private String email;
        private String code;
    }
}
