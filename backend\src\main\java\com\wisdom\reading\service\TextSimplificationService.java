package com.wisdom.reading.service;

import com.wisdom.reading.dto.QualityAssessment;
import com.wisdom.reading.dto.TextSimplificationRequest;
import com.wisdom.reading.dto.TextSimplificationResponse;

import java.util.List;

/**
 * 文本简化服务接口
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
public interface TextSimplificationService {

    /**
     * 简化文本
     * 
     * @param request 简化请求
     * @return 简化结果
     */
    TextSimplificationResponse simplifyText(TextSimplificationRequest request);

    /**
     * 基于分析结果简化文本
     * 
     * @param analysisId 分析ID
     * @param targetGrade 目标年级
     * @param simplificationLevel 简化程度
     * @return 简化结果
     */
    TextSimplificationResponse simplifyFromAnalysis(Long analysisId, String targetGrade, String simplificationLevel);

    /**
     * 批量简化文本
     * 
     * @param requests 简化请求列表
     * @return 简化结果列表
     */
    List<TextSimplificationResponse> batchSimplify(List<TextSimplificationRequest> requests);

    /**
     * 获取简化历史
     * 
     * @param page 页码
     * @param size 每页大小
     * @param targetGrade 目标年级筛选
     * @return 简化历史列表
     */
    List<TextSimplificationResponse> getSimplificationHistory(Integer page, Integer size, String targetGrade);

    /**
     * 获取简化详情
     * 
     * @param simplificationId 简化ID
     * @return 简化详情
     */
    TextSimplificationResponse getSimplificationDetail(Long simplificationId);

    /**
     * 删除简化记录
     * 
     * @param simplificationId 简化ID
     */
    void deleteSimplification(Long simplificationId);

    /**
     * 比较简化效果
     * 
     * @param simplificationId 简化ID
     * @return 比较结果
     */
    TextSimplificationResponse.ComparisonResult compareSimplification(Long simplificationId);

    /**
     * 重新简化
     *
     * @param simplificationId 原简化ID
     * @param newTargetGrade 新目标年级
     * @param newSimplificationLevel 新简化程度
     * @return 新的简化结果
     */
    TextSimplificationResponse resimplify(Long simplificationId, String newTargetGrade, String newSimplificationLevel);

    /**
     * 评估简化效果
     *
     * @param simplificationId 简化ID
     * @return 评估结果
     */
    QualityAssessment evaluateSimplification(Long simplificationId);
}
