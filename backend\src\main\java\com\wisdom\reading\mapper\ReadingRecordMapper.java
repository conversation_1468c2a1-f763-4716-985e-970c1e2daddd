package com.wisdom.reading.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wisdom.reading.entity.ReadingRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 阅读记录Mapper接口
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Mapper
public interface ReadingRecordMapper extends BaseMapper<ReadingRecord> {

    /**
     * 根据用户ID查询阅读记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 阅读记录列表
     */
    @Select("SELECT * FROM reading_records WHERE user_id = #{userId} AND deleted = 0 ORDER BY create_time DESC LIMIT #{limit}")
    List<ReadingRecord> findByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据分析ID查询阅读记录
     * 
     * @param analysisId 分析ID
     * @return 阅读记录列表
     */
    @Select("SELECT * FROM reading_records WHERE analysis_id = #{analysisId} AND deleted = 0 ORDER BY create_time DESC")
    List<ReadingRecord> findByAnalysisId(@Param("analysisId") Long analysisId);

    /**
     * 查询用户在指定时间范围内的阅读记录
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 阅读记录列表
     */
    @Select("SELECT * FROM reading_records WHERE user_id = #{userId} AND create_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0 ORDER BY create_time DESC")
    List<ReadingRecord> findByUserIdAndTimeRange(@Param("userId") Long userId, 
                                                @Param("startTime") LocalDateTime startTime, 
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户阅读数据
     * 
     * @param userId 用户ID
     * @return 阅读统计
     */
    ReadingStats getReadingStats(@Param("userId") Long userId);

    /**
     * 获取用户阅读进度统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 进度统计列表
     */
    List<ReadingProgress> getReadingProgress(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 获取热门阅读文本
     * 
     * @param gradeLevel 年级水平
     * @param limit 限制数量
     * @return 热门文本列表
     */
    List<PopularText> getPopularTexts(@Param("gradeLevel") String gradeLevel, @Param("limit") Integer limit);

    /**
     * 阅读统计信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ReadingStats {
        private Long totalReadingTime;
        private Integer totalTextsRead;
        private Integer completedTexts;
        private Double averageProgress;
        private String favoriteGrade;
        private String favoriteCategory;
    }

    /**
     * 阅读进度信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ReadingProgress {
        private String date;
        private Integer textsRead;
        private Long readingTime;
        private Double averageProgress;
    }

    /**
     * 热门文本信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class PopularText {
        private String textTitle;
        private String textGrade;
        private Integer readCount;
        private Double averageProgress;
        private Double averageRating;
    }
}
