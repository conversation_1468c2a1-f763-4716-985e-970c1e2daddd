package com.wisdom.reading.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 教学资源实体类
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("teaching_resources")
public class TeachingResource {

    /**
     * 资源ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 关联的文本分析ID
     */
    @TableField("analysis_id")
    private Long analysisId;

    /**
     * 资源标题
     */
    @TableField("title")
    private String title;

    /**
     * 资源类型：VOCABULARY(词汇卡片), QUESTIONS(理解题目), EXERCISES(练习题), TEACHING_PLAN(教学计划)
     */
    @TableField("resource_type")
    private String resourceType;

    /**
     * 目标年级
     */
    @TableField("target_grade")
    private String targetGrade;

    /**
     * 年级水平（别名，用于兼容）
     */
    public String getGradeLevel() {
        return this.targetGrade;
    }

    public void setGradeLevel(String gradeLevel) {
        this.targetGrade = gradeLevel;
    }

    /**
     * 学生水平：BEGINNER(初级), INTERMEDIATE(中级), ADVANCED(高级)
     */
    @TableField("student_level")
    private String studentLevel;

    /**
     * 资源内容（JSON格式）
     */
    @TableField("content")
    private String content;

    /**
     * 资源描述
     */
    @TableField("description")
    private String description;

    /**
     * 难度等级 (1-10)
     */
    @TableField("difficulty_level")
    private Integer difficultyLevel;

    /**
     * 预计使用时间（分钟）
     */
    @TableField("estimated_time")
    private Integer estimatedTime;

    /**
     * 使用次数
     */
    @TableField("usage_count")
    private Integer usageCount;

    /**
     * 平均评分
     */
    @TableField("average_rating")
    private Double averageRating;

    /**
     * 评价次数
     */
    @TableField("rating_count")
    private Integer ratingCount;

    /**
     * 是否公开
     */
    @TableField("is_public")
    private Boolean isPublic;

    /**
     * 状态：ACTIVE(活跃), INACTIVE(非活跃), ARCHIVED(已归档)
     */
    @TableField("status")
    private String status;

    /**
     * 标签（JSON格式）
     */
    @TableField("tags")
    private String tags;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
