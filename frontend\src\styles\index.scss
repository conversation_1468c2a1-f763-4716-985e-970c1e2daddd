// 导入变量
@use './variables.scss' as *;

// 全局样式重置
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, sans-serif;
  font-size: $font-size-base;
  color: $text-primary;
  background-color: $background-light;
}

#app {
  min-height: 100vh;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 响应式工具类
.hidden-xs {
  @media (max-width: 767px) {
    display: none !important;
  }
}

.hidden-sm {
  @media (min-width: 768px) and (max-width: 991px) {
    display: none !important;
  }
}

.hidden-md {
  @media (min-width: 992px) and (max-width: 1199px) {
    display: none !important;
  }
}

.hidden-lg {
  @media (min-width: 1200px) {
    display: none !important;
  }
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// Element Plus 样式覆盖
.el-button {
  border-radius: $border-radius-base;
}

.el-card {
  border-radius: $border-radius-large;
  box-shadow: $box-shadow-light;
}