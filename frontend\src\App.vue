<template>
  <div id="app">
    <div v-if="appReady">
      <router-view />
    </div>
    <div v-else class="app-loading">
      <div class="loading-spinner"></div>
      <div>应用初始化中...</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      appReady: false
    }
  },
  async mounted() {
    try {
      // 应用初始化逻辑
      await this.initApp()
      this.appReady = true
    } catch (error) {
      console.error('应用初始化失败:', error)
      // 即使初始化失败也显示应用
      this.appReady = true
    }
  },
  methods: {
    async initApp() {
      // 初始化主题
      this.initTheme()

      // 设置页面标题
      document.title = '智慧阅读 - AI文本难度分级系统'

      // 延迟检查用户登录状态，避免阻塞应用启动
      setTimeout(() => {
        this.$store.dispatch('user/checkLoginStatus').catch(() => {
          // 忽略登录状态检查失败
          console.log('用户登录状态检查失败，继续启动应用')
        })
      }, 100)
    },

    initTheme() {
      // 从本地存储获取主题设置
      const theme = localStorage.getItem('wisdom-reading-theme') || 'light'
      document.documentElement.setAttribute('data-theme', theme)
    }
  }
}
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  background-color: var(--el-bg-color-page);
}

.app-loading {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #606266;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e4e7ed;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
  
  &:hover {
    background: var(--el-border-color-dark);
  }
}

// 主题变量
:root {
  --wisdom-primary-color: #409EFF;
  --wisdom-success-color: #67C23A;
  --wisdom-warning-color: #E6A23C;
  --wisdom-danger-color: #F56C6C;
  --wisdom-info-color: #909399;
  
  // 分级颜色
  --grade-primary-low: #67C23A;
  --grade-primary-mid: #E6A23C;
  --grade-primary-high: #F56C6C;
  --grade-junior-high: #909399;
  --grade-senior-high: #606266;
}

// 暗色主题
[data-theme="dark"] {
  --wisdom-bg-color: #1a1a1a;
  --wisdom-text-color: #e5e5e5;
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }

.p-10 { padding: 10px; }
.p-20 { padding: 20px; }

// 动画
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s;
}
.slide-enter-from {
  transform: translateX(-100%);
}
.slide-leave-to {
  transform: translateX(100%);
}
</style>
