<template>
  <div class="text-library">
    <div class="page-header">
      <h1>文本库</h1>
      <p>丰富的分级阅读文本资源库</p>
    </div>
    
    <div class="library-controls">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="searchQuery"
            placeholder="搜索文本标题、内容或标签..."
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="showUploadDialog = true">
            <el-icon><Plus /></el-icon>
            上传文本
          </el-button>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="filter-row">
        <el-col :span="6">
          <el-select v-model="filters.difficulty" placeholder="难度等级" clearable @change="applyFilters">
            <el-option label="小学低年级" value="primary-low" />
            <el-option label="小学中年级" value="primary-mid" />
            <el-option label="小学高年级" value="primary-high" />
            <el-option label="初中" value="junior-high" />
            <el-option label="高中" value="senior-high" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.category" placeholder="文本类型" clearable @change="applyFilters">
            <el-option label="记叙文" value="narrative" />
            <el-option label="说明文" value="expository" />
            <el-option label="议论文" value="argumentative" />
            <el-option label="诗歌" value="poetry" />
            <el-option label="科普文章" value="science" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.length" placeholder="文本长度" clearable @change="applyFilters">
            <el-option label="短文 (<500字)" value="short" />
            <el-option label="中等 (500-1500字)" value="medium" />
            <el-option label="长文 (>1500字)" value="long" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button @click="resetFilters">重置筛选</el-button>
        </el-col>
      </el-row>
    </div>
    
    <div class="library-content">
      <el-row :gutter="20">
        <el-col :span="6" v-for="text in filteredTexts" :key="text.id">
          <el-card class="text-card" @click="viewText(text)">
            <div class="text-cover">
              <img v-if="text.cover" :src="text.cover" :alt="text.title" />
              <div v-else class="default-cover">
                <el-icon size="48"><Document /></el-icon>
              </div>
            </div>
            
            <div class="text-info">
              <h3>{{ text.title }}</h3>
              <p class="text-author">{{ text.author }}</p>
              <p class="text-summary">{{ text.summary }}</p>
              
              <div class="text-meta">
                <el-tag :type="getDifficultyType(text.difficulty)" size="small">
                  {{ getDifficultyLabel(text.difficulty) }}
                </el-tag>
                <el-tag type="info" size="small">{{ text.category }}</el-tag>
                <span class="text-length">{{ text.wordCount }}字</span>
              </div>
              
              <div class="text-actions">
                <el-button size="small" type="primary" @click.stop="viewText(text)">
                  阅读
                </el-button>
                <el-button size="small" @click.stop="analyzeText(text)">
                  分析
                </el-button>
                <el-button size="small" @click.stop="downloadText(text)">
                  下载
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <div v-if="filteredTexts.length === 0" class="empty-state">
        <el-empty description="没有找到符合条件的文本" />
      </div>
      
      <el-pagination
        v-if="totalTexts > pageSize"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalTexts"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 上传文本对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文本" width="50%">
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="文本标题" required>
          <el-input v-model="uploadForm.title" />
        </el-form-item>
        <el-form-item label="作者">
          <el-input v-model="uploadForm.author" />
        </el-form-item>
        <el-form-item label="文本内容" required>
          <el-input v-model="uploadForm.content" type="textarea" :rows="8" />
        </el-form-item>
        <el-form-item label="难度等级">
          <el-select v-model="uploadForm.difficulty">
            <el-option label="小学低年级" value="primary-low" />
            <el-option label="小学中年级" value="primary-mid" />
            <el-option label="小学高年级" value="primary-high" />
            <el-option label="初中" value="junior-high" />
            <el-option label="高中" value="senior-high" />
          </el-select>
        </el-form-item>
        <el-form-item label="文本类型">
          <el-select v-model="uploadForm.category">
            <el-option label="记叙文" value="narrative" />
            <el-option label="说明文" value="expository" />
            <el-option label="议论文" value="argumentative" />
            <el-option label="诗歌" value="poetry" />
            <el-option label="科普文章" value="science" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button type="primary" @click="uploadText">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Search, Plus, Document } from '@element-plus/icons-vue'

export default {
  name: 'TextLibrary',
  components: {
    Search,
    Plus,
    Document
  },
  data() {
    return {
      searchQuery: '',
      showUploadDialog: false,
      currentPage: 1,
      pageSize: 12,
      totalTexts: 0,
      filters: {
        difficulty: '',
        category: '',
        length: ''
      },
      uploadForm: {
        title: '',
        author: '',
        content: '',
        difficulty: '',
        category: ''
      },
      texts: [
        {
          id: 1,
          title: '春天的故事',
          author: '张三',
          summary: '描述春天美丽景色的散文，适合小学生阅读...',
          difficulty: 'primary-low',
          category: '记叙文',
          wordCount: 350,
          cover: null
        },
        {
          id: 2,
          title: '科学探索之旅',
          author: '李四',
          summary: '介绍现代科学发展历程的科普文章...',
          difficulty: 'junior-high',
          category: '科普文章',
          wordCount: 1200,
          cover: null
        }
        // 更多文本数据...
      ]
    }
  },
  computed: {
    filteredTexts() {
      let result = this.texts
      
      // 搜索过滤
      if (this.searchQuery) {
        result = result.filter(text => 
          text.title.includes(this.searchQuery) ||
          text.author.includes(this.searchQuery) ||
          text.summary.includes(this.searchQuery)
        )
      }
      
      // 难度过滤
      if (this.filters.difficulty) {
        result = result.filter(text => text.difficulty === this.filters.difficulty)
      }
      
      // 类型过滤
      if (this.filters.category) {
        result = result.filter(text => text.category === this.filters.category)
      }
      
      // 长度过滤
      if (this.filters.length) {
        result = result.filter(text => {
          if (this.filters.length === 'short') return text.wordCount < 500
          if (this.filters.length === 'medium') return text.wordCount >= 500 && text.wordCount <= 1500
          if (this.filters.length === 'long') return text.wordCount > 1500
          return true
        })
      }
      
      this.totalTexts = result.length
      return result.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
    }
  },
  methods: {
    handleSearch() {
      this.currentPage = 1
    },
    
    applyFilters() {
      this.currentPage = 1
    },
    
    resetFilters() {
      this.filters = {
        difficulty: '',
        category: '',
        length: ''
      }
      this.searchQuery = ''
      this.currentPage = 1
    },
    
    viewText(text) {
      this.$message.info(`正在打开文本：${text.title}`)
      // 实际项目中会跳转到文本阅读页面
    },
    
    analyzeText(text) {
      this.$message.info(`正在分析文本：${text.title}`)
      // 实际项目中会跳转到文本分析页面
    },
    
    downloadText(text) {
      this.$message.success(`开始下载：${text.title}`)
      // 实际项目中会下载文本文件
    },
    
    uploadText() {
      if (!this.uploadForm.title || !this.uploadForm.content) {
        this.$message.warning('请填写必要信息')
        return
      }
      
      // 模拟上传
      this.$message.success('文本上传成功')
      this.showUploadDialog = false
      this.uploadForm = {
        title: '',
        author: '',
        content: '',
        difficulty: '',
        category: ''
      }
    },
    
    getDifficultyType(difficulty) {
      const types = {
        'primary-low': 'success',
        'primary-mid': 'success',
        'primary-high': 'warning',
        'junior-high': 'warning',
        'senior-high': 'danger'
      }
      return types[difficulty] || 'info'
    },
    
    getDifficultyLabel(difficulty) {
      const labels = {
        'primary-low': '小学低年级',
        'primary-mid': '小学中年级',
        'primary-high': '小学高年级',
        'junior-high': '初中',
        'senior-high': '高中'
      }
      return labels[difficulty] || '未知'
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.text-library {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.library-controls {
  margin-bottom: 30px;
  
  .filter-row {
    margin-top: 16px;
  }
}

.text-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 400px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.text-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-fill-color-lighter);
  margin-bottom: 16px;
  border-radius: 4px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }
  
  .default-cover {
    color: var(--el-text-color-placeholder);
  }
}

.text-info {
  h3 {
    margin-bottom: 8px;
    color: var(--el-text-color-primary);
    font-size: 16px;
    line-height: 1.4;
  }
  
  .text-author {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin-bottom: 8px;
  }
  
  .text-summary {
    color: var(--el-text-color-secondary);
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.text-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  
  .text-length {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
  }
}

.text-actions {
  display: flex;
  gap: 8px;
  
  .el-button {
    flex: 1;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.el-pagination {
  margin-top: 30px;
  text-align: center;
}
</style>
