<template>
  <div class="teaching-tools">
    <div class="page-header">
      <h1>教学工具</h1>
      <p>为教师提供智能化的教学辅助工具</p>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="8" v-for="tool in tools" :key="tool.id">
        <el-card class="tool-card" @click="openTool(tool)" :class="{ 'tool-active': tool.active }">
          <div class="tool-icon">
            <el-icon :size="48" :color="tool.color">
              <component :is="tool.icon" />
            </el-icon>
          </div>
          
          <div class="tool-content">
            <h3>{{ tool.name }}</h3>
            <p>{{ tool.description }}</p>
            
            <div class="tool-features">
              <el-tag v-for="feature in tool.features" :key="feature" size="small" type="info">
                {{ feature }}
              </el-tag>
            </div>
          </div>
          
          <div class="tool-actions">
            <el-button type="primary" size="small" @click.stop="openTool(tool)">
              使用工具
            </el-button>
            <el-button size="small" @click.stop="viewDetails(tool)">
              查看详情
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 工具详情对话框 -->
    <el-dialog v-model="dialogVisible" :title="selectedTool?.name" width="60%">
      <div v-if="selectedTool" class="tool-details">
        <div class="tool-description">
          <h4>工具介绍</h4>
          <p>{{ selectedTool.fullDescription }}</p>
        </div>
        
        <div class="tool-usage">
          <h4>使用方法</h4>
          <ol>
            <li v-for="step in selectedTool.usageSteps" :key="step">{{ step }}</li>
          </ol>
        </div>
        
        <div class="tool-examples">
          <h4>应用场景</h4>
          <ul>
            <li v-for="example in selectedTool.examples" :key="example">{{ example }}</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="openTool(selectedTool)">开始使用</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Document, Edit, DataAnalysis, Setting, Star, Tools } from '@element-plus/icons-vue'

export default {
  name: 'TeachingTools',
  components: {
    Document,
    Edit,
    DataAnalysis,
    Setting,
    Star,
    Tools
  },
  data() {
    return {
      dialogVisible: false,
      selectedTool: null,
      tools: [
        {
          id: 1,
          name: '题目生成器',
          description: '基于文本内容自动生成阅读理解题目',
          icon: 'Document',
          color: '#409EFF',
          features: ['自动生成', '多种题型', '难度调节'],
          active: false,
          fullDescription: '智能题目生成器可以根据输入的文本内容，自动生成多种类型的阅读理解题目，包括选择题、填空题、简答题等。系统会分析文本的关键信息点，生成符合教学要求的高质量题目。',
          usageSteps: [
            '输入或上传要生成题目的文本内容',
            '选择题目类型和数量',
            '设置难度等级',
            '点击生成按钮',
            '预览和编辑生成的题目',
            '导出题目到Word或PDF格式'
          ],
          examples: [
            '为课文生成配套练习题',
            '制作阅读理解测试卷',
            '创建课堂互动问题',
            '准备家庭作业题目'
          ]
        },
        {
          id: 2,
          name: '课件制作助手',
          description: '智能辅助制作教学课件和演示文稿',
          icon: 'Edit',
          color: '#67C23A',
          features: ['模板丰富', '智能排版', '多媒体支持'],
          active: false,
          fullDescription: '课件制作助手提供丰富的教学模板和智能排版功能，帮助教师快速制作专业的教学课件。支持文本、图片、音频、视频等多种媒体元素的整合。',
          usageSteps: [
            '选择适合的课件模板',
            '输入教学内容和要点',
            '添加多媒体素材',
            '调整布局和样式',
            '预览课件效果',
            '导出为PPT或PDF格式'
          ],
          examples: [
            '制作语文课文讲解课件',
            '创建互动式教学演示',
            '准备家长会展示材料',
            '制作学生作品展示'
          ]
        },
        {
          id: 3,
          name: '学情分析器',
          description: '分析学生学习数据，生成个性化报告',
          icon: 'DataAnalysis',
          color: '#E6A23C',
          features: ['数据可视化', '趋势分析', '个性化建议'],
          active: false,
          fullDescription: '学情分析器通过收集和分析学生的学习数据，包括阅读速度、理解准确率、学习时长等，生成详细的学习报告和个性化的学习建议。',
          usageSteps: [
            '导入学生学习数据',
            '选择分析维度和时间范围',
            '运行智能分析算法',
            '查看可视化分析结果',
            '生成个性化学习建议',
            '导出分析报告'
          ],
          examples: [
            '分析班级整体学习情况',
            '跟踪个别学生进步情况',
            '识别学习困难点',
            '制定针对性教学计划'
          ]
        },
        {
          id: 4,
          name: '教学计划器',
          description: '智能制定和管理教学计划',
          icon: 'Setting',
          color: '#F56C6C',
          features: ['智能规划', '进度跟踪', '资源整合'],
          active: false,
          fullDescription: '教学计划器帮助教师制定科学合理的教学计划，包括课程安排、进度规划、资源配置等。支持多种教学模式和个性化定制。',
          usageSteps: [
            '设置教学目标和要求',
            '选择教学内容和资源',
            '制定时间安排',
            '分配教学任务',
            '设置评估标准',
            '生成完整教学计划'
          ],
          examples: [
            '制定学期教学计划',
            '安排单元教学进度',
            '规划复习备考计划',
            '设计项目式学习方案'
          ]
        },
        {
          id: 5,
          name: '评估工具箱',
          description: '多样化的学习评估和测试工具',
          icon: 'Star',
          color: '#909399',
          features: ['多元评估', '自动评分', '反馈生成'],
          active: false,
          fullDescription: '评估工具箱提供多种评估方式和工具，包括在线测试、作业评分、学习档案等，支持形成性评估和总结性评估。',
          usageSteps: [
            '选择评估类型和方式',
            '设计评估内容和标准',
            '发布评估任务',
            '收集学生作答数据',
            '自动评分和分析',
            '生成评估报告和反馈'
          ],
          examples: [
            '在线阅读理解测试',
            '作文自动评分',
            '学习进度评估',
            '综合能力测评'
          ]
        },
        {
          id: 6,
          name: '资源管理器',
          description: '统一管理和分享教学资源',
          icon: 'Tools',
          color: '#722ED1',
          features: ['云端存储', '分类管理', '协作分享'],
          active: false,
          fullDescription: '资源管理器提供统一的教学资源管理平台，支持文档、图片、音视频等多种格式的资源存储、分类和分享。',
          usageSteps: [
            '上传教学资源文件',
            '创建分类和标签',
            '设置访问权限',
            '分享给其他教师',
            '搜索和使用资源',
            '维护资源库'
          ],
          examples: [
            '建立个人教学资源库',
            '与同事分享优质资源',
            '创建学科资源集合',
            '管理多媒体教学素材'
          ]
        }
      ]
    }
  },
  methods: {
    openTool(tool) {
      // 这里应该跳转到具体的工具页面或打开工具界面
      this.$message.info(`正在打开 ${tool.name}...`)
      // 实际项目中可能会路由跳转或打开新窗口
    },
    
    viewDetails(tool) {
      this.selectedTool = tool
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.teaching-tools {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  
  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.tool-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 280px;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
  
  &.tool-active {
    border-color: var(--el-color-primary);
  }
}

.tool-icon {
  text-align: center;
  margin-bottom: 16px;
}

.tool-content {
  text-align: center;
  margin-bottom: 20px;
  
  h3 {
    margin-bottom: 8px;
    color: var(--el-text-color-primary);
  }
  
  p {
    color: var(--el-text-color-regular);
    line-height: 1.5;
    margin-bottom: 12px;
  }
}

.tool-features {
  display: flex;
  justify-content: center;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.tool-actions {
  text-align: center;
  
  .el-button {
    margin: 0 4px;
  }
}

.tool-details {
  .tool-description,
  .tool-usage,
  .tool-examples {
    margin-bottom: 24px;
    
    h4 {
      color: var(--el-text-color-primary);
      margin-bottom: 12px;
    }
    
    p, li {
      color: var(--el-text-color-regular);
      line-height: 1.6;
    }
    
    ol, ul {
      padding-left: 20px;
    }
    
    li {
      margin-bottom: 6px;
    }
  }
}
</style>
