package com.wisdom.reading.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 教学资源响应DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
public class TeachingResourceResponse {

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 关联的分析ID
     */
    private Long analysisId;

    /**
     * 资源标题
     */
    private String title;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 适用年级
     */
    private String gradeLevel;

    /**
     * 目标年级
     */
    private String targetGrade;

    /**
     * 难度等级
     */
    private Integer difficultyLevel;

    /**
     * 学生水平
     */
    private String studentLevel;

    /**
     * 生词卡片
     */
    private VocabularyCards vocabularyCards;

    /**
     * 阅读理解题
     */
    private ComprehensionQuestions comprehensionQuestions;

    /**
     * 阅读指导
     */
    private ReadingGuide readingGuide;

    /**
     * 练习题
     */
    private Exercises exercises;

    /**
     * 资源统计
     */
    private ResourceStats stats;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 资源描述
     */
    private String description;

    /**
     * 预计使用时间（分钟）
     */
    private Integer estimatedTime;

    /**
     * 平均评分
     */
    private Double averageRating;

    /**
     * 使用次数
     */
    private Integer usageCount;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 生词卡片
     */
    @Data
    public static class VocabularyCards {
        /**
         * 卡片列表
         */
        private List<VocabularyCard> cards;

        /**
         * 总数量
         */
        private Integer totalCount;

        /**
         * 难度分布
         */
        private DifficultyDistribution difficultyDistribution;
    }

    /**
     * 生词卡片
     */
    @Data
    public static class VocabularyCard {
        /**
         * 词汇
         */
        private String word;

        /**
         * 拼音/发音
         */
        private String pronunciation;

        /**
         * 释义
         */
        private String meaning;

        /**
         * 词性
         */
        private String partOfSpeech;

        /**
         * 例句
         */
        private String example;

        /**
         * 图片URL
         */
        private String imageUrl;

        /**
         * 难度等级
         */
        private String difficultyLevel;

        /**
         * 在原文中的位置
         */
        private List<Integer> positions;

        /**
         * 同义词
         */
        private List<String> synonyms;

        /**
         * 反义词
         */
        private List<String> antonyms;
    }

    /**
     * 阅读理解题
     */
    @Data
    public static class ComprehensionQuestions {
        /**
         * 题目列表
         */
        private List<Question> questions;

        /**
         * 总数量
         */
        private Integer totalCount;

        /**
         * 总分值
         */
        private Integer totalPoints;

        /**
         * 预计完成时间（分钟）
         */
        private Integer estimatedTime;
    }

    /**
     * 题目
     */
    @Data
    public static class Question {
        /**
         * 题目ID
         */
        private String questionId;

        /**
         * 题目类型
         */
        private String type;

        /**
         * 题目内容
         */
        private String question;

        /**
         * 选项（选择题）
         */
        private List<String> options;

        /**
         * 正确答案
         */
        private String correctAnswer;

        /**
         * 答案解析
         */
        private String explanation;

        /**
         * 分值
         */
        private Integer points;

        /**
         * 难度等级
         */
        private String difficulty;

        /**
         * 考查能力
         */
        private List<String> skills;
    }

    /**
     * 阅读指导
     */
    @Data
    public static class ReadingGuide {
        /**
         * 预习步骤
         */
        private List<String> preparationSteps;

        /**
         * 阅读策略
         */
        private List<String> readingStrategies;

        /**
         * 讨论要点
         */
        private List<String> discussionPoints;

        /**
         * 拓展活动
         */
        private List<String> extensionActivities;

        /**
         * 重点关注
         */
        private List<String> focusPoints;

        /**
         * 预计时间
         */
        private String timeEstimate;

        /**
         * 教学建议
         */
        private List<String> teachingTips;
    }

    /**
     * 练习题
     */
    @Data
    public static class Exercises {
        /**
         * 词汇练习
         */
        private List<VocabularyExercise> vocabularyExercises;

        /**
         * 句式练习
         */
        private List<SentenceExercise> sentenceExercises;

        /**
         * 理解练习
         */
        private List<ComprehensionExercise> comprehensionExercises;
    }

    /**
     * 词汇练习
     */
    @Data
    public static class VocabularyExercise {
        private String type; // FILL_BLANK, MATCH, CHOOSE
        private String instruction;
        private String content;
        private List<String> options;
        private String answer;
    }

    /**
     * 句式练习
     */
    @Data
    public static class SentenceExercise {
        private String type; // REWRITE, COMBINE, ANALYZE
        private String instruction;
        private String sentence;
        private String answer;
        private String explanation;
    }

    /**
     * 理解练习
     */
    @Data
    public static class ComprehensionExercise {
        private String type; // SUMMARY, INFERENCE, ANALYSIS
        private String instruction;
        private String content;
        private String sampleAnswer;
    }

    /**
     * 难度分布
     */
    @Data
    public static class DifficultyDistribution {
        private Integer basicCount;
        private Integer intermediateCount;
        private Integer advancedCount;
    }

    /**
     * 资源统计
     */
    @Data
    public static class ResourceStats {
        private Integer vocabularyCount;
        private Integer questionCount;
        private Integer exerciseCount;
        private Integer totalItems;
        private String complexity;
    }
}
