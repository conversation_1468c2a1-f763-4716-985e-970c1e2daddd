<template>
  <div class="text-analysis">
    <div class="page-header">
      <h1>文本分析</h1>
      <p>AI智能分析文本难度等级，为教学提供科学依据</p>
    </div>
    
    <el-card class="analysis-card">
      <template #header>
        <div class="card-header">
          <span>文本输入</span>
          <el-button type="primary" @click="analyzeText" :loading="analyzing">
            开始分析
          </el-button>
        </div>
      </template>
      
      <el-form :model="form" label-width="100px">
        <el-form-item label="文本内容">
          <el-input
            v-model="form.text"
            type="textarea"
            :rows="8"
            placeholder="请输入要分析的文本内容..."
            maxlength="5000"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="分析类型">
          <el-checkbox-group v-model="form.analysisTypes">
            <el-checkbox label="difficulty">难度等级</el-checkbox>
            <el-checkbox label="vocabulary">词汇分析</el-checkbox>
            <el-checkbox label="syntax">句法分析</el-checkbox>
            <el-checkbox label="readability">可读性评估</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card v-if="analysisResult" class="result-card">
      <template #header>
        <span>分析结果</span>
      </template>
      
      <div class="analysis-results">
        <div class="result-item" v-if="analysisResult.difficulty">
          <h3>难度等级</h3>
          <el-tag :type="getDifficultyType(analysisResult.difficulty.level)" size="large">
            {{ analysisResult.difficulty.label }}
          </el-tag>
          <p>{{ analysisResult.difficulty.description }}</p>
        </div>
        
        <div class="result-item" v-if="analysisResult.vocabulary">
          <h3>词汇分析</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="总词数">{{ analysisResult.vocabulary.totalWords }}</el-descriptions-item>
            <el-descriptions-item label="独特词数">{{ analysisResult.vocabulary.uniqueWords }}</el-descriptions-item>
            <el-descriptions-item label="平均词长">{{ analysisResult.vocabulary.avgWordLength }}</el-descriptions-item>
            <el-descriptions-item label="词汇丰富度">{{ analysisResult.vocabulary.richness }}%</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'TextAnalysis',
  data() {
    return {
      analyzing: false,
      form: {
        text: '',
        analysisTypes: ['difficulty', 'vocabulary']
      },
      analysisResult: null
    }
  },
  methods: {
    async analyzeText() {
      if (!this.form.text.trim()) {
        this.$message.warning('请输入要分析的文本内容')
        return
      }
      
      this.analyzing = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        this.analysisResult = {
          difficulty: {
            level: 'intermediate',
            label: '中等难度',
            description: '适合初中及以上学生阅读'
          },
          vocabulary: {
            totalWords: 150,
            uniqueWords: 98,
            avgWordLength: 4.2,
            richness: 65
          }
        }
        
        this.$message.success('分析完成')
      } catch (error) {
        this.$message.error('分析失败，请重试')
      } finally {
        this.analyzing = false
      }
    },
    
    getDifficultyType(level) {
      const types = {
        easy: 'success',
        intermediate: 'warning',
        hard: 'danger'
      }
      return types[level] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.text-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.analysis-card, .result-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-results {
  .result-item {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h3 {
      margin-bottom: 12px;
      color: var(--el-text-color-primary);
    }
  }
}
</style>
