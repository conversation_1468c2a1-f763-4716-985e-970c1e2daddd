{"name": "wisdom-reading-frontend", "version": "1.0.0", "description": "智慧阅读系统前端", "private": true, "scripts": {"dev": "vite", "serve": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.jsx,.ts,.tsx --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "element-plus": "^2.3.8", "highlight.js": "^11.8.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "marked": "^5.1.1", "nprogress": "^0.2.0", "vue": "^3.3.4", "vue-echarts": "^6.6.1", "vue-router": "^4.2.4", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-standard": "^8.0.1", "eslint": "^8.45.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.33.0", "sass": "^1.64.1", "vite": "^7.0.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}