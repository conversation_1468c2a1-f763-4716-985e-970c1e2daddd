package com.wisdom.reading.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 文本简化记录实体类
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("text_simplification")
public class TextSimplification {

    /**
     * 简化记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 关联的文本分析ID
     */
    @TableField("analysis_id")
    private Long analysisId;

    /**
     * 原始文本
     */
    @TableField("original_text")
    private String originalText;

    /**
     * 简化后文本
     */
    @TableField("simplified_text")
    private String simplifiedText;

    /**
     * 目标年级
     */
    @TableField("target_grade")
    private String targetGrade;

    /**
     * 简化程度
     */
    @TableField("simplification_level")
    private String simplificationLevel;

    /**
     * 简化前分级
     */
    @TableField("original_grade")
    private String originalGrade;

    /**
     * 简化后分级
     */
    @TableField("simplified_grade")
    private String simplifiedGrade;

    /**
     * 生词替换列表（JSON格式）
     */
    @TableField("word_replacements")
    private String wordReplacements;

    /**
     * 句式调整说明（JSON格式）
     */
    @TableField("sentence_adjustments")
    private String sentenceAdjustments;

    /**
     * 简化比例 (0-1)
     */
    @TableField("simplification_ratio")
    private Double simplificationRatio;

    /**
     * 简化质量评分 (0-100)
     */
    @TableField("quality_score")
    private Double qualityScore;

    /**
     * 处理状态：PENDING(处理中), SUCCESS(成功), FAILED(失败)
     */
    @TableField("status")
    private String status;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
