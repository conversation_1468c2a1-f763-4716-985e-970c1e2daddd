{"version": 3, "file": "walk.js", "sources": ["../src/index.js"], "sourcesContent": ["// AST walker module for Mozilla Parser API compatible trees\n\n// A simple walk is one where you simply specify callbacks to be\n// called on specific nodes. The last two arguments are optional. A\n// simple use would be\n//\n//     walk.simple(myTree, {\n//         Expression: function(node) { ... }\n//     });\n//\n// to do something with all expressions. All Parser API node types\n// can be used to identify node types, as well as Expression and\n// Statement, which denote categories of nodes.\n//\n// The base argument can be used to pass a custom (recursive)\n// walker, and state can be used to give this walked an initial\n// state.\n\nexport function simple(node, visitors, baseVisitor, state, override) {\n  if (!baseVisitor) baseVisitor = base\n  ;(function c(node, st, override) {\n    let type = override || node.type, found = visitors[type]\n    baseVisitor[type](node, st, c)\n    if (found) found(node, st)\n  })(node, state, override)\n}\n\n// An ancestor walk keeps an array of ancestor nodes (including the\n// current node) and passes them to the callback as third parameter\n// (and also as state parameter when no other state is present).\nexport function ancestor(node, visitors, baseVisitor, state) {\n  let ancestors = []\n  if (!baseVisitor) baseVisitor = base\n  ;(function c(node, st, override) {\n    let type = override || node.type, found = visitors[type]\n    let isNew = node !== ancestors[ancestors.length - 1]\n    if (isNew) ancestors.push(node)\n    baseVisitor[type](node, st, c)\n    if (found) found(node, st || ancestors, ancestors)\n    if (isNew) ancestors.pop()\n  })(node, state)\n}\n\n// A recursive walk is one where your functions override the default\n// walkers. They can modify and replace the state parameter that's\n// threaded through the walk, and can opt how and whether to walk\n// their child nodes (by calling their third argument on these\n// nodes).\nexport function recursive(node, state, funcs, baseVisitor, override) {\n  let visitor = funcs ? make(funcs, baseVisitor || undefined) : baseVisitor\n  ;(function c(node, st, override) {\n    visitor[override || node.type](node, st, c)\n  })(node, state, override)\n}\n\nfunction makeTest(test) {\n  if (typeof test === \"string\")\n    return type => type === test\n  else if (!test)\n    return () => true\n  else\n    return test\n}\n\nclass Found {\n  constructor(node, state) { this.node = node; this.state = state }\n}\n\n// A full walk triggers the callback on each node\nexport function full(node, callback, baseVisitor, state, override) {\n  if (!baseVisitor) baseVisitor = base\n  ;(function c(node, st, override) {\n    let type = override || node.type\n    baseVisitor[type](node, st, c)\n    if (!override) callback(node, st, type)\n  })(node, state, override)\n}\n\n// An fullAncestor walk is like an ancestor walk, but triggers\n// the callback on each node\nexport function fullAncestor(node, callback, baseVisitor, state) {\n  if (!baseVisitor) baseVisitor = base\n  let ancestors = []\n  ;(function c(node, st, override) {\n    let type = override || node.type\n    let isNew = node !== ancestors[ancestors.length - 1]\n    if (isNew) ancestors.push(node)\n    baseVisitor[type](node, st, c)\n    if (!override) callback(node, st || ancestors, ancestors, type)\n    if (isNew) ancestors.pop()\n  })(node, state)\n}\n\n// Find a node with a given start, end, and type (all are optional,\n// null can be used as wildcard). Returns a {node, state} object, or\n// undefined when it doesn't find a matching node.\nexport function findNodeAt(node, start, end, test, baseVisitor, state) {\n  if (!baseVisitor) baseVisitor = base\n  test = makeTest(test)\n  try {\n    (function c(node, st, override) {\n      let type = override || node.type\n      if ((start == null || node.start <= start) &&\n          (end == null || node.end >= end))\n        baseVisitor[type](node, st, c)\n      if ((start == null || node.start === start) &&\n          (end == null || node.end === end) &&\n          test(type, node))\n        throw new Found(node, st)\n    })(node, state)\n  } catch (e) {\n    if (e instanceof Found) return e\n    throw e\n  }\n}\n\n// Find the innermost node of a given type that contains the given\n// position. Interface similar to findNodeAt.\nexport function findNodeAround(node, pos, test, baseVisitor, state) {\n  test = makeTest(test)\n  if (!baseVisitor) baseVisitor = base\n  try {\n    (function c(node, st, override) {\n      let type = override || node.type\n      if (node.start > pos || node.end < pos) return\n      baseVisitor[type](node, st, c)\n      if (test(type, node)) throw new Found(node, st)\n    })(node, state)\n  } catch (e) {\n    if (e instanceof Found) return e\n    throw e\n  }\n}\n\n// Find the outermost matching node after a given position.\nexport function findNodeAfter(node, pos, test, baseVisitor, state) {\n  test = makeTest(test)\n  if (!baseVisitor) baseVisitor = base\n  try {\n    (function c(node, st, override) {\n      if (node.end < pos) return\n      let type = override || node.type\n      if (node.start >= pos && test(type, node)) throw new Found(node, st)\n      baseVisitor[type](node, st, c)\n    })(node, state)\n  } catch (e) {\n    if (e instanceof Found) return e\n    throw e\n  }\n}\n\n// Find the outermost matching node before a given position.\nexport function findNodeBefore(node, pos, test, baseVisitor, state) {\n  test = makeTest(test)\n  if (!baseVisitor) baseVisitor = base\n  let max\n  ;(function c(node, st, override) {\n    if (node.start > pos) return\n    let type = override || node.type\n    if (node.end <= pos && (!max || max.node.end < node.end) && test(type, node))\n      max = new Found(node, st)\n    baseVisitor[type](node, st, c)\n  })(node, state)\n  return max\n}\n\n// Fallback to an Object.create polyfill for older environments.\nconst create = Object.create || function(proto) {\n  function Ctor() {}\n  Ctor.prototype = proto\n  return new Ctor\n}\n\n// Used to create a custom walker. Will fill in all missing node\n// type properties with the defaults.\nexport function make(funcs, baseVisitor) {\n  let visitor = create(baseVisitor || base)\n  for (let type in funcs) visitor[type] = funcs[type]\n  return visitor\n}\n\nfunction skipThrough(node, st, c) { c(node, st) }\nfunction ignore(_node, _st, _c) {}\n\n// Node walkers.\n\nexport const base = {}\n\nbase.Program = base.BlockStatement = (node, st, c) => {\n  for (let stmt of node.body)\n    c(stmt, st, \"Statement\")\n}\nbase.Statement = skipThrough\nbase.EmptyStatement = ignore\nbase.ExpressionStatement = base.ParenthesizedExpression =\n  (node, st, c) => c(node.expression, st, \"Expression\")\nbase.IfStatement = (node, st, c) => {\n  c(node.test, st, \"Expression\")\n  c(node.consequent, st, \"Statement\")\n  if (node.alternate) c(node.alternate, st, \"Statement\")\n}\nbase.LabeledStatement = (node, st, c) => c(node.body, st, \"Statement\")\nbase.BreakStatement = base.ContinueStatement = ignore\nbase.WithStatement = (node, st, c) => {\n  c(node.object, st, \"Expression\")\n  c(node.body, st, \"Statement\")\n}\nbase.SwitchStatement = (node, st, c) => {\n  c(node.discriminant, st, \"Expression\")\n  for (let cs of node.cases) {\n    if (cs.test) c(cs.test, st, \"Expression\")\n    for (let cons of cs.consequent)\n      c(cons, st, \"Statement\")\n  }\n}\nbase.SwitchCase = (node, st, c) => {\n  if (node.test) c(node.test, st, \"Expression\")\n  for (let cons of node.consequent)\n    c(cons, st, \"Statement\")\n}\nbase.ReturnStatement = base.YieldExpression = base.AwaitExpression = (node, st, c) => {\n  if (node.argument) c(node.argument, st, \"Expression\")\n}\nbase.ThrowStatement = base.SpreadElement =\n  (node, st, c) => c(node.argument, st, \"Expression\")\nbase.TryStatement = (node, st, c) => {\n  c(node.block, st, \"Statement\")\n  if (node.handler) c(node.handler, st)\n  if (node.finalizer) c(node.finalizer, st, \"Statement\")\n}\nbase.CatchClause = (node, st, c) => {\n  if (node.param) c(node.param, st, \"Pattern\")\n  c(node.body, st, \"Statement\")\n}\nbase.WhileStatement = base.DoWhileStatement = (node, st, c) => {\n  c(node.test, st, \"Expression\")\n  c(node.body, st, \"Statement\")\n}\nbase.ForStatement = (node, st, c) => {\n  if (node.init) c(node.init, st, \"ForInit\")\n  if (node.test) c(node.test, st, \"Expression\")\n  if (node.update) c(node.update, st, \"Expression\")\n  c(node.body, st, \"Statement\")\n}\nbase.ForInStatement = base.ForOfStatement = (node, st, c) => {\n  c(node.left, st, \"ForInit\")\n  c(node.right, st, \"Expression\")\n  c(node.body, st, \"Statement\")\n}\nbase.ForInit = (node, st, c) => {\n  if (node.type === \"VariableDeclaration\") c(node, st)\n  else c(node, st, \"Expression\")\n}\nbase.DebuggerStatement = ignore\n\nbase.FunctionDeclaration = (node, st, c) => c(node, st, \"Function\")\nbase.VariableDeclaration = (node, st, c) => {\n  for (let decl of node.declarations)\n    c(decl, st)\n}\nbase.VariableDeclarator = (node, st, c) => {\n  c(node.id, st, \"Pattern\")\n  if (node.init) c(node.init, st, \"Expression\")\n}\n\nbase.Function = (node, st, c) => {\n  if (node.id) c(node.id, st, \"Pattern\")\n  for (let param of node.params)\n    c(param, st, \"Pattern\")\n  c(node.body, st, node.expression ? \"Expression\" : \"Statement\")\n}\n\nbase.Pattern = (node, st, c) => {\n  if (node.type === \"Identifier\")\n    c(node, st, \"VariablePattern\")\n  else if (node.type === \"MemberExpression\")\n    c(node, st, \"MemberPattern\")\n  else\n    c(node, st)\n}\nbase.VariablePattern = ignore\nbase.MemberPattern = skipThrough\nbase.RestElement = (node, st, c) => c(node.argument, st, \"Pattern\")\nbase.ArrayPattern = (node, st, c) => {\n  for (let elt of node.elements) {\n    if (elt) c(elt, st, \"Pattern\")\n  }\n}\nbase.ObjectPattern = (node, st, c) => {\n  for (let prop of node.properties) {\n    if (prop.type === \"Property\") {\n      if (prop.computed) c(prop.key, st, \"Expression\")\n      c(prop.value, st, \"Pattern\")\n    } else if (prop.type === \"RestElement\") {\n      c(prop.argument, st, \"Pattern\")\n    }\n  }\n}\n\nbase.Expression = skipThrough\nbase.ThisExpression = base.Super = base.MetaProperty = ignore\nbase.ArrayExpression = (node, st, c) => {\n  for (let elt of node.elements) {\n    if (elt) c(elt, st, \"Expression\")\n  }\n}\nbase.ObjectExpression = (node, st, c) => {\n  for (let prop of node.properties)\n    c(prop, st)\n}\nbase.FunctionExpression = base.ArrowFunctionExpression = base.FunctionDeclaration\nbase.SequenceExpression = (node, st, c) => {\n  for (let expr of node.expressions)\n    c(expr, st, \"Expression\")\n}\nbase.TemplateLiteral = (node, st, c) => {\n  for (let quasi of node.quasis)\n    c(quasi, st)\n\n  for (let expr of node.expressions)\n    c(expr, st, \"Expression\")\n}\nbase.TemplateElement = ignore\nbase.UnaryExpression = base.UpdateExpression = (node, st, c) => {\n  c(node.argument, st, \"Expression\")\n}\nbase.BinaryExpression = base.LogicalExpression = (node, st, c) => {\n  c(node.left, st, \"Expression\")\n  c(node.right, st, \"Expression\")\n}\nbase.AssignmentExpression = base.AssignmentPattern = (node, st, c) => {\n  c(node.left, st, \"Pattern\")\n  c(node.right, st, \"Expression\")\n}\nbase.ConditionalExpression = (node, st, c) => {\n  c(node.test, st, \"Expression\")\n  c(node.consequent, st, \"Expression\")\n  c(node.alternate, st, \"Expression\")\n}\nbase.NewExpression = base.CallExpression = (node, st, c) => {\n  c(node.callee, st, \"Expression\")\n  if (node.arguments)\n    for (let arg of node.arguments)\n      c(arg, st, \"Expression\")\n}\nbase.MemberExpression = (node, st, c) => {\n  c(node.object, st, \"Expression\")\n  if (node.computed) c(node.property, st, \"Expression\")\n}\nbase.ExportNamedDeclaration = base.ExportDefaultDeclaration = (node, st, c) => {\n  if (node.declaration)\n    c(node.declaration, st, node.type === \"ExportNamedDeclaration\" || node.declaration.id ? \"Statement\" : \"Expression\")\n  if (node.source) c(node.source, st, \"Expression\")\n}\nbase.ExportAllDeclaration = (node, st, c) => {\n  c(node.source, st, \"Expression\")\n}\nbase.ImportDeclaration = (node, st, c) => {\n  for (let spec of node.specifiers)\n    c(spec, st)\n  c(node.source, st, \"Expression\")\n}\nbase.ImportSpecifier = base.ImportDefaultSpecifier = base.ImportNamespaceSpecifier = base.Identifier = base.Literal = ignore\n\nbase.TaggedTemplateExpression = (node, st, c) => {\n  c(node.tag, st, \"Expression\")\n  c(node.quasi, st, \"Expression\")\n}\nbase.ClassDeclaration = base.ClassExpression = (node, st, c) => c(node, st, \"Class\")\nbase.Class = (node, st, c) => {\n  if (node.id) c(node.id, st, \"Pattern\")\n  if (node.superClass) c(node.superClass, st, \"Expression\")\n  c(node.body, st)\n}\nbase.ClassBody = (node, st, c) => {\n  for (let elt of node.body)\n    c(elt, st)\n}\nbase.MethodDefinition = base.Property = (node, st, c) => {\n  if (node.computed) c(node.key, st, \"Expression\")\n  c(node.value, st, \"Expression\")\n}\n"], "names": ["let", "const"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;;;;AAkBA,AAAO,SAAS,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;EACnE,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,IAAI;GACnC,EAAA,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;IAC/BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAC;IACxD,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;IAC9B,IAAI,KAAK,EAAE,EAAA,KAAK,CAAC,IAAI,EAAE,EAAE,EAAC,EAAA;GAC3B,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC;CAC1B;;;;;AAKD,AAAO,SAAS,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE;EAC3DA,IAAI,SAAS,GAAG,GAAE;EAClB,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,IAAI;GACnC,EAAA,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;IAC/BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAC;IACxDA,IAAI,KAAK,GAAG,IAAI,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAC;IACpD,IAAI,KAAK,EAAE,EAAA,SAAS,CAAC,IAAI,CAAC,IAAI,EAAC,EAAA;IAC/B,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;IAC9B,IAAI,KAAK,EAAE,EAAA,KAAK,CAAC,IAAI,EAAE,EAAE,IAAI,SAAS,EAAE,SAAS,EAAC,EAAA;IAClD,IAAI,KAAK,EAAE,EAAA,SAAS,CAAC,GAAG,GAAE,EAAA;GAC3B,EAAE,IAAI,EAAE,KAAK,EAAC;CAChB;;;;;;;AAOD,AAAO,SAAS,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE;EACnEA,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,IAAI,SAAS,CAAC,GAAG,WAAW,CACxE,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;IAC/B,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;GAC5C,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC;CAC1B;;AAED,SAAS,QAAQ,CAAC,IAAI,EAAE;EACtB,IAAI,OAAO,IAAI,KAAK,QAAQ;IAC1B,EAAA,OAAO,UAAA,IAAI,EAAC,SAAG,IAAI,KAAK,IAAI,GAAA,EAAA;OACzB,IAAI,CAAC,IAAI;IACZ,EAAA,OAAO,YAAG,SAAG,IAAI,GAAA,EAAA;;IAEjB,EAAA,OAAO,IAAI,EAAA;CACd;;AAED,IAAM,KAAK,GAAC,cACC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,MAAK,EAAE,CAAA;;;AAInE,AAAO,SAAS,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;EACjE,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,IAAI;GACnC,EAAA,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;IAC/BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,KAAI;IAChC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;IAC9B,IAAI,CAAC,QAAQ,EAAE,EAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,EAAA;GACxC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC;CAC1B;;;;AAID,AAAO,SAAS,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE;EAC/D,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,KAAI,EAAA;EACpCA,IAAI,SAAS,GAAG,EAAE,CACjB,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;IAC/BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,KAAI;IAChCA,IAAI,KAAK,GAAG,IAAI,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAC;IACpD,IAAI,KAAK,EAAE,EAAA,SAAS,CAAC,IAAI,CAAC,IAAI,EAAC,EAAA;IAC/B,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;IAC9B,IAAI,CAAC,QAAQ,EAAE,EAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,SAAS,EAAE,SAAS,EAAE,IAAI,EAAC,EAAA;IAC/D,IAAI,KAAK,EAAE,EAAA,SAAS,CAAC,GAAG,GAAE,EAAA;GAC3B,EAAE,IAAI,EAAE,KAAK,EAAC;CAChB;;;;;AAKD,AAAO,SAAS,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE;EACrE,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,KAAI,EAAA;EACpC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAC;EACrB,IAAI;IACF,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;MAC9BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,KAAI;MAChC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK;WACpC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC;QAClC,EAAA,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC,EAAA;MAChC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK;WACrC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC;UACjC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;QAClB,EAAA,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAA;KAC5B,EAAE,IAAI,EAAE,KAAK,EAAC;GAChB,CAAC,OAAO,CAAC,EAAE;IACV,IAAI,CAAC,YAAY,KAAK,EAAE,EAAA,OAAO,CAAC,EAAA;IAChC,MAAM,CAAC;GACR;CACF;;;;AAID,AAAO,SAAS,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE;EAClE,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAC;EACrB,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,KAAI,EAAA;EACpC,IAAI;IACF,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;MAC9BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,KAAI;MAChC,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,MAAM,EAAA;MAC9C,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;MAC9B,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAA;KAChD,EAAE,IAAI,EAAE,KAAK,EAAC;GAChB,CAAC,OAAO,CAAC,EAAE;IACV,IAAI,CAAC,YAAY,KAAK,EAAE,EAAA,OAAO,CAAC,EAAA;IAChC,MAAM,CAAC;GACR;CACF;;;AAGD,AAAO,SAAS,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE;EACjE,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAC;EACrB,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,KAAI,EAAA;EACpC,IAAI;IACF,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;MAC9B,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAA,MAAM,EAAA;MAC1BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,KAAI;MAChC,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAA;MACpE,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;KAC/B,EAAE,IAAI,EAAE,KAAK,EAAC;GAChB,CAAC,OAAO,CAAC,EAAE;IACV,IAAI,CAAC,YAAY,KAAK,EAAE,EAAA,OAAO,CAAC,EAAA;IAChC,MAAM,CAAC;GACR;CACF;;;AAGD,AAAO,SAAS,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE;EAClE,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAC;EACrB,IAAI,CAAC,WAAW,EAAE,EAAA,WAAW,GAAG,KAAI,EAAA;EACpCA,IAAI,GAAG,CACN,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;IAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,EAAA,MAAM,EAAA;IAC5BA,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,KAAI;IAChC,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;MAC1E,EAAA,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,EAAC,EAAA;IAC3B,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAC;GAC/B,EAAE,IAAI,EAAE,KAAK,EAAC;EACf,OAAO,GAAG;CACX;;;AAGDC,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,KAAK,EAAE;EAC9C,SAAS,IAAI,GAAG,EAAE;EAClB,IAAI,CAAC,SAAS,GAAG,MAAK;EACtB,OAAO,IAAI,IAAI;EAChB;;;;AAID,AAAO,SAAS,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE;EACvCD,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW,IAAI,IAAI,EAAC;EACzC,KAAKA,IAAI,IAAI,IAAI,KAAK,EAAE,EAAA,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAC,EAAA;EACnD,OAAO,OAAO;CACf;;AAED,SAAS,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,EAAC,EAAE;AACjD,SAAS,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;;;;AAIlC,AAAOC,IAAM,IAAI,GAAG,GAAE;;AAEtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACjD,KAAa,kBAAI,IAAI,CAAC,IAAI,yBAAA;IAArB;IAAAD,IAAI,IAAI;;IACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;GAAA;EAC3B;AACD,IAAI,CAAC,SAAS,GAAG,YAAW;AAC5B,IAAI,CAAC,cAAc,GAAG,OAAM;AAC5B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB;EACrD,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,SAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,YAAY,CAAC,IAAA;AACvD,IAAI,CAAC,WAAW,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC/B,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC;EAC9B,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,WAAW,EAAC;EACnC,IAAI,IAAI,CAAC,SAAS,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE,WAAW,EAAC,EAAA;EACvD;AACD,IAAI,CAAC,gBAAgB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,SAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,CAAC,IAAA;AACtE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,GAAG,OAAM;AACrD,IAAI,CAAC,aAAa,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACjC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,EAAC;EAChC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;EAC9B;AACD,IAAI,CAAC,eAAe,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACnC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,YAAY,EAAC;EACtC,KAAW,kBAAI,IAAI,CAAC,KAAK,yBAAA,EAAE;IAAtBA,IAAI,EAAE;;IACT,IAAI,EAAE,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;IACzC,KAAa,sBAAI,EAAE,CAAC,UAAU,+BAAA;MAAzB;MAAAA,IAAI,IAAI;;MACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;KAAA;GAC3B;EACF;AACD,IAAI,CAAC,UAAU,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC9B,IAAI,IAAI,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EAC7C,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA;IAA3B;IAAAA,IAAI,IAAI;;IACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;GAAA;EAC3B;AACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACjF,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EACtD;AACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;EACtC,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,SAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,CAAC,IAAA;AACrD,IAAI,CAAC,YAAY,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAChC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,WAAW,EAAC;EAC9B,IAAI,IAAI,CAAC,OAAO,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,EAAC,EAAA;EACrC,IAAI,IAAI,CAAC,SAAS,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE,WAAW,EAAC,EAAA;EACvD;AACD,IAAI,CAAC,WAAW,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC/B,IAAI,IAAI,CAAC,KAAK,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,SAAS,EAAC,EAAA;EAC5C,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;EAC9B;AACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC1D,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC;EAC9B,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;EAC9B;AACD,IAAI,CAAC,YAAY,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAChC,IAAI,IAAI,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC,EAAA;EAC1C,IAAI,IAAI,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EAC7C,IAAI,IAAI,CAAC,MAAM,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EACjD,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;EAC9B;AACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACxD,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC;EAC3B,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,YAAY,EAAC;EAC/B,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAC;EAC9B;AACD,IAAI,CAAC,OAAO,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB,EAAE,EAAA,CAAC,CAAC,IAAI,EAAE,EAAE,EAAC,EAAA;OAC/C,EAAA,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EAC/B;AACD,IAAI,CAAC,iBAAiB,GAAG,OAAM;;AAE/B,IAAI,CAAC,mBAAmB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,SAAG,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,UAAU,CAAC,IAAA;AACnE,IAAI,CAAC,mBAAmB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACvC,KAAa,kBAAI,IAAI,CAAC,YAAY,yBAAA;IAA7B;IAAAA,IAAI,IAAI;;IACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAC;GAAA;EACd;AACD,IAAI,CAAC,kBAAkB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACtC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAC;EACzB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EAC9C;;AAED,IAAI,CAAC,QAAQ,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC5B,IAAI,IAAI,CAAC,EAAE,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAC,EAAA;EACtC,KAAc,kBAAI,IAAI,CAAC,MAAM,yBAAA;IAAxB;IAAAA,IAAI,KAAK;;IACZ,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,SAAS,EAAC;GAAA;EACzB,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,GAAG,YAAY,GAAG,WAAW,EAAC;EAC/D;;AAED,IAAI,CAAC,OAAO,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY;IAC5B,EAAA,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,iBAAiB,EAAC,EAAA;OAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB;IACvC,EAAA,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,eAAe,EAAC,EAAA;;IAE5B,EAAA,CAAC,CAAC,IAAI,EAAE,EAAE,EAAC,EAAA;EACd;AACD,IAAI,CAAC,eAAe,GAAG,OAAM;AAC7B,IAAI,CAAC,aAAa,GAAG,YAAW;AAChC,IAAI,CAAC,WAAW,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,SAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,CAAC,IAAA;AACnE,IAAI,CAAC,YAAY,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAChC,KAAY,kBAAI,IAAI,CAAC,QAAQ,yBAAA,EAAE;IAA1BA,IAAI,GAAG;;IACV,IAAI,GAAG,EAAE,EAAA,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,SAAS,EAAC,EAAA;GAC/B;EACF;AACD,IAAI,CAAC,aAAa,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACjC,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA,EAAE;IAA7BA,IAAI,IAAI;;IACX,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;MAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;MAChD,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,SAAS,EAAC;KAC7B,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;MACtC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAC;KAChC;GACF;EACF;;AAED,IAAI,CAAC,UAAU,GAAG,YAAW;AAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,OAAM;AAC7D,IAAI,CAAC,eAAe,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACnC,KAAY,kBAAI,IAAI,CAAC,QAAQ,yBAAA,EAAE;IAA1BA,IAAI,GAAG;;IACV,IAAI,GAAG,EAAE,EAAA,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;GAClC;EACF;AACD,IAAI,CAAC,gBAAgB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACpC,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA;IAA3B;IAAAA,IAAI,IAAI;;IACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAC;GAAA;EACd;AACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,oBAAmB;AACjF,IAAI,CAAC,kBAAkB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACtC,KAAa,kBAAI,IAAI,CAAC,WAAW,yBAAA;IAA5B;IAAAA,IAAI,IAAI;;IACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC;GAAA;EAC5B;AACD,IAAI,CAAC,eAAe,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACnC,KAAc,kBAAI,IAAI,CAAC,MAAM,yBAAA;IAAxB;IAAAA,IAAI,KAAK;;IACZ,CAAC,CAAC,KAAK,EAAE,EAAE,EAAC;GAAA;;EAEd,KAAa,sBAAI,IAAI,CAAC,WAAW,+BAAA;IAA5B;IAAAA,IAAI,IAAI;;IACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC;GAAA;EAC5B;AACD,IAAI,CAAC,eAAe,GAAG,OAAM;AAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC3D,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAC;EACnC;AACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC7D,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC;EAC9B,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,YAAY,EAAC;EAChC;AACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACjE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC;EAC3B,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,YAAY,EAAC;EAChC;AACD,IAAI,CAAC,qBAAqB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACzC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAC;EAC9B,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,YAAY,EAAC;EACpC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE,YAAY,EAAC;EACpC;AACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACvD,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,EAAC;EAChC,IAAI,IAAI,CAAC,SAAS;IAChB,EAAA,KAAY,kBAAI,IAAI,CAAC,SAAS,yBAAA;MAAzB;QAAAA,IAAI,GAAG;;QACV,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,YAAY,EAAC;OAAA,EAAA;EAC7B;AACD,IAAI,CAAC,gBAAgB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACpC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,EAAC;EAChC,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EACtD;AACD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,wBAAwB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC1E,IAAI,IAAI,CAAC,WAAW;IAClB,EAAA,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,wBAAwB,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,GAAG,YAAY,EAAC,EAAA;EACrH,IAAI,IAAI,CAAC,MAAM,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EAClD;AACD,IAAI,CAAC,oBAAoB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACxC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,EAAC;EACjC;AACD,IAAI,CAAC,iBAAiB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACrC,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA;IAA3B;IAAAA,IAAI,IAAI;;IACX,CAAC,CAAC,IAAI,EAAE,EAAE,EAAC;GAAA;EACb,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,EAAC;EACjC;AACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,GAAG,OAAM;;AAE5H,IAAI,CAAC,wBAAwB,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,YAAY,EAAC;EAC7B,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,YAAY,EAAC;EAChC;AACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,SAAG,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,IAAA;AACpF,IAAI,CAAC,KAAK,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACzB,IAAI,IAAI,CAAC,EAAE,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAC,EAAA;EACtC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EACzD,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAC;EACjB;AACD,IAAI,CAAC,SAAS,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EAC7B,KAAY,kBAAI,IAAI,CAAC,IAAI,yBAAA;IAApB;IAAAA,IAAI,GAAG;;IACV,CAAC,CAAC,GAAG,EAAE,EAAE,EAAC;GAAA;EACb;AACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;EACpD,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,YAAY,EAAC,EAAA;EAChD,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,YAAY,EAAC;CAChC;;;;;;;;;;;;;;;;;;;;;;"}