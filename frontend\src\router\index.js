import { createRouter, createWebHistory } from 'vue-router'

const Home = () => import('@/views/Home/index.vue')
const TextAnalysis = () => import('@/views/TextAnalysis/index.vue')

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/text-analysis',
    name: 'TextAnalysis',
    component: TextAnalysis
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
