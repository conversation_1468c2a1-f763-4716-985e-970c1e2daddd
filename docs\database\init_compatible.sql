-- 智慧阅读系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS wisdom_reading DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; USE wisdom_reading; -- 用户表
CREATE TABLE users ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID', username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名', password VARCHAR(255) NOT NULL COMMENT '密码', email VARCHAR(100) UNIQUE COMMENT '邮箱', phone VARCHAR(20) COMMENT '手机号', real_name VARCHAR(50) COMMENT '真实姓名', role ENUM('STUDENT', 'PARENT', 'TEACHER', 'ADMIN') NOT NULL DEFAULT 'STUDENT' COMMENT '用户角色', status ENUM('ACTIVE', 'INACTIVE', 'BANNED') NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态', avatar VARCHAR(255) COMMENT '头像URL', grade VARCHAR(20) COMMENT '年级（学生用户）', school VARCHAR(100) COMMENT '学校名称', class_name VARCHAR(50) COMMENT '班级（学生用户）', student_id BIGINT COMMENT '家长关联的学生ID', last_login_time DATETIME COMMENT '最后登录时间', create_time DATETIME NOT NULL COMMENT '创建时间', update_time DATETIME NOT NULL COMMENT '更新时间', deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志', INDEX idx_username (username), INDEX idx_email (email), INDEX idx_role (role), INDEX idx_grade (grade), INDEX idx_create_time (create_time)
) COMMENT '用户表'; -- 文本分析表
CREATE TABLE text_analysis ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分析记录ID', user_id BIGINT COMMENT '用户ID', original_text LONGTEXT NOT NULL COMMENT '原始文本内容', title VARCHAR(200) COMMENT '文本标题', source VARCHAR(500) COMMENT '文本来源', grade_level ENUM('PRIMARY_LOW', 'PRIMARY_MID', 'PRIMARY_HIGH', 'JUNIOR_HIGH', 'SENIOR_HIGH') COMMENT '分级结果', confidence DECIMAL(3,2) COMMENT '分级置信度', text_length INT COMMENT '文本长度（字符数）', word_count INT COMMENT '词汇总数', sentence_count INT COMMENT '句子总数', paragraph_count INT COMMENT '段落总数', new_word_rate DECIMAL(3,2) COMMENT '生词率', avg_sentence_length DECIMAL(5,2) COMMENT '平均句长', complex_sentence_rate DECIMAL(3,2) COMMENT '复杂句比例', topic_category VARCHAR(50) COMMENT '主题分类', keywords JSON COMMENT '关键词列表', new_words JSON COMMENT '生词列表', long_sentences JSON COMMENT '长句列表', status ENUM('PENDING', 'SUCCESS', 'FAILED') NOT NULL DEFAULT 'PENDING' COMMENT '分析状态', error_message TEXT COMMENT '错误信息', create_time DATETIME NOT NULL COMMENT '创建时间', update_time DATETIME NOT NULL COMMENT '更新时间', deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志', INDEX idx_user_id (user_id), INDEX idx_grade_level (grade_level), INDEX idx_status (status), INDEX idx_create_time (create_time), FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) COMMENT '文本分析记录表'; -- 文本简化表
CREATE TABLE text_simplification ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '简化记录ID', user_id BIGINT COMMENT '用户ID', analysis_id BIGINT COMMENT '关联的文本分析ID', original_text LONGTEXT NOT NULL COMMENT '原始文本', simplified_text LONGTEXT COMMENT '简化后文本', target_grade ENUM('PRIMARY_LOW', 'PRIMARY_MID', 'PRIMARY_HIGH', 'JUNIOR_HIGH', 'SENIOR_HIGH') NOT NULL COMMENT '目标年级', original_grade ENUM('PRIMARY_LOW', 'PRIMARY_MID', 'PRIMARY_HIGH', 'JUNIOR_HIGH', 'SENIOR_HIGH') COMMENT '简化前分级', simplified_grade ENUM('PRIMARY_LOW', 'PRIMARY_MID', 'PRIMARY_HIGH', 'JUNIOR_HIGH', 'SENIOR_HIGH') COMMENT '简化后分级', word_replacements JSON COMMENT '生词替换列表', sentence_adjustments JSON COMMENT '句式调整说明', simplification_ratio DECIMAL(3,2) COMMENT '简化比例', quality_score DECIMAL(5,2) COMMENT '简化质量评分', status ENUM('PENDING', 'SUCCESS', 'FAILED') NOT NULL DEFAULT 'PENDING' COMMENT '处理状态', error_message TEXT COMMENT '错误信息', create_time DATETIME NOT NULL COMMENT '创建时间', update_time DATETIME NOT NULL COMMENT '更新时间', deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志', INDEX idx_user_id (user_id), INDEX idx_analysis_id (analysis_id), INDEX idx_target_grade (target_grade), INDEX idx_status (status), INDEX idx_create_time (create_time), FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL, FOREIGN KEY (analysis_id) REFERENCES text_analysis(id) ON DELETE SET NULL
) COMMENT '文本简化记录表'; -- 阅读记录表
CREATE TABLE reading_records ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID', user_id BIGINT NOT NULL COMMENT '用户ID', analysis_id BIGINT COMMENT '关联的文本分析ID', text_title VARCHAR(200) COMMENT '文本标题', text_grade ENUM('PRIMARY_LOW', 'PRIMARY_MID', 'PRIMARY_HIGH', 'JUNIOR_HIGH', 'SENIOR_HIGH') COMMENT '文本分级', reading_duration INT COMMENT '阅读时长（秒）', reading_progress DECIMAL(3,2) COMMENT '阅读进度', status ENUM('READING', 'COMPLETED', 'PAUSED') NOT NULL DEFAULT 'READING' COMMENT '阅读状态', rating TINYINT COMMENT '阅读评分（1-5）', notes TEXT COMMENT '阅读笔记', marked_words JSON COMMENT '生词标记', comprehension_score DECIMAL(5,2) COMMENT '阅读理解得分', start_time DATETIME COMMENT '开始阅读时间', finish_time DATETIME COMMENT '完成阅读时间', create_time DATETIME NOT NULL COMMENT '创建时间', update_time DATETIME NOT NULL COMMENT '更新时间', deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志', INDEX idx_user_id (user_id), INDEX idx_analysis_id (analysis_id), INDEX idx_text_grade (text_grade), INDEX idx_status (status), INDEX idx_create_time (create_time), FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE, FOREIGN KEY (analysis_id) REFERENCES text_analysis(id) ON DELETE SET NULL
) COMMENT '阅读记录表'; -- 教学资源表
CREATE TABLE teaching_resources ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '资源ID', analysis_id BIGINT NOT NULL COMMENT '关联的文本分析ID', user_id BIGINT COMMENT '创建用户ID', resource_type ENUM('VOCABULARY_CARDS', 'QUESTIONS', 'READING_GUIDE', 'EXERCISES') NOT NULL COMMENT '资源类型', title VARCHAR(200) NOT NULL COMMENT '资源标题', content JSON NOT NULL COMMENT '资源内容', grade_level ENUM('PRIMARY_LOW', 'PRIMARY_MID', 'PRIMARY_HIGH', 'JUNIOR_HIGH', 'SENIOR_HIGH') COMMENT '适用年级', difficulty_level ENUM('EASY', 'MEDIUM', 'HARD') COMMENT '难度等级', usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数', rating DECIMAL(3,2) COMMENT '资源评分', create_time DATETIME NOT NULL COMMENT '创建时间', update_time DATETIME NOT NULL COMMENT '更新时间', deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志', INDEX idx_analysis_id (analysis_id), INDEX idx_user_id (user_id), INDEX idx_resource_type (resource_type), INDEX idx_grade_level (grade_level), INDEX idx_create_time (create_time), FOREIGN KEY (analysis_id) REFERENCES text_analysis(id) ON DELETE CASCADE, FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) COMMENT '教学资源表'; -- 文本库表
CREATE TABLE text_library ( id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文本ID', title VARCHAR(200) NOT NULL COMMENT '文本标题', content LONGTEXT NOT NULL COMMENT '文本内容', author VARCHAR(100) COMMENT '作者', source VARCHAR(200) COMMENT '来源', grade_level ENUM('PRIMARY_LOW', 'PRIMARY_MID', 'PRIMARY_HIGH', 'JUNIOR_HIGH', 'SENIOR_HIGH') NOT NULL COMMENT '分级', category VARCHAR(50) COMMENT '分类', text_type ENUM('NARRATIVE', 'EXPOSITORY', 'ARGUMENTATIVE', 'POETRY') COMMENT '文体类型', difficulty_score DECIMAL(3,2) COMMENT '难度评分', word_count INT COMMENT '字数', reading_time INT COMMENT '预计阅读时间（分钟）', tags JSON COMMENT '标签', is_recommended TINYINT NOT NULL DEFAULT 0 COMMENT '是否推荐', view_count INT NOT NULL DEFAULT 0 COMMENT '查看次数', like_count INT NOT NULL DEFAULT 0 COMMENT '点赞次数', create_time DATETIME NOT NULL COMMENT '创建时间', update_time DATETIME NOT NULL COMMENT '更新时间', deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志', INDEX idx_grade_level (grade_level), INDEX idx_category (category), INDEX idx_text_type (text_type), INDEX idx_difficulty_score (difficulty_score), INDEX idx_is_recommended (is_recommended), INDEX idx_create_time (create_time), FULLTEXT idx_content (title, content)
) COMMENT '文本库表'; -- 插入初始管理员用户
INSERT INTO users (username, password, email, real_name, role, status) VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiE.rV8QLjhJpjNJheOYjqyDYi', '<EMAIL>', '系统管理员', 'ADMIN', 'ACTIVE'); -- 插入示例文本数据
INSERT INTO text_library (title, content, author, source, grade_level, category, text_type, difficulty_score, word_count, reading_time, tags, is_recommended) VALUES ('小猫钓鱼', '从前有一只小猫，它很喜欢钓鱼。每天早上，小猫都会拿着鱼竿到河边钓鱼。但是小猫总是不专心，一会儿看蝴蝶，一会儿追蜻蜓，所以总是钓不到鱼。后来，小猫学会了专心致志，终于钓到了大鱼。', '民间故事', '传统故事', 'PRIMARY_LOW', '寓言故事', 'NARRATIVE', 0.3, 120, 3, '["专注", "坚持", "寓言"]', 1),
('春天来了', '春天来了，大地苏醒了。小草从土里钻出来，绿绿的，嫩嫩的。桃花开了，粉红粉红的，像小朋友的笑脸。柳树发芽了，长长的柳条像小姑娘的辫子。燕子从南方飞回来了，在天空中快乐地飞翔。', '教材选文', '小学语文', 'PRIMARY_LOW', '自然', 'EXPOSITORY', 0.25, 100, 2, '["春天", "自然", "描写"]', 1);
