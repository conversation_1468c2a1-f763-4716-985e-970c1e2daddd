<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="VulnerableLibrariesLocal" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="isIgnoringEnabled" value="true" />
      <option name="ignoredModules">
        <list>
          <option value="reading-backend" />
        </list>
      </option>
      <option name="ignoredPackages">
        <list>
          <option value="com.jayway.jsonpath:json-path:2.7.0" />
        </list>
      </option>
      <option name="ignoredReasons">
        <list>
          <option value="Not exploitable" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>