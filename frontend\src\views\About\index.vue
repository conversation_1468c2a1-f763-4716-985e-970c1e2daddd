<template>
  <div class="about">
    <div class="hero-section">
      <div class="hero-content">
        <h1>智慧阅读</h1>
        <p class="hero-subtitle">AI驱动的文本难度分级与阅读辅助系统</p>
        <p class="hero-description">
          运用先进的人工智能技术，为教育工作者和学习者提供智能化的文本分析、
          难度评估和个性化阅读建议，让阅读教学更科学、更高效。
        </p>
      </div>
    </div>
    
    <div class="features-section">
      <div class="section-header">
        <h2>核心功能</h2>
        <p>为不同用户群体提供专业的阅读辅助工具</p>
      </div>
      
      <el-row :gutter="30">
        <el-col :span="8" v-for="feature in features" :key="feature.id">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon :size="48" :color="feature.color">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="technology-section">
      <div class="section-header">
        <h2>技术优势</h2>
        <p>基于最新的AI技术和教育理论</p>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="tech-content">
            <h3>先进的AI算法</h3>
            <ul>
              <li>自然语言处理技术</li>
              <li>深度学习模型</li>
              <li>语义理解分析</li>
              <li>智能文本生成</li>
            </ul>
            
            <h3>科学的评估体系</h3>
            <ul>
              <li>多维度难度评估</li>
              <li>个性化学习路径</li>
              <li>实时进度跟踪</li>
              <li>数据驱动决策</li>
            </ul>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="tech-stats">
            <div class="stat-item">
              <div class="stat-number">99.2%</div>
              <div class="stat-label">难度评估准确率</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">50万+</div>
              <div class="stat-label">文本库容量</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">10万+</div>
              <div class="stat-label">用户数量</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">95%</div>
              <div class="stat-label">用户满意度</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="team-section">
      <div class="section-header">
        <h2>开发团队</h2>
        <p>来自教育和技术领域的专业团队</p>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="6" v-for="member in teamMembers" :key="member.id">
          <div class="team-card">
            <div class="member-avatar">
              <img v-if="member.avatar" :src="member.avatar" :alt="member.name" />
              <el-icon v-else size="48"><User /></el-icon>
            </div>
            <h4>{{ member.name }}</h4>
            <p class="member-role">{{ member.role }}</p>
            <p class="member-description">{{ member.description }}</p>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="contact-section">
      <div class="section-header">
        <h2>联系我们</h2>
        <p>我们期待与您的合作与交流</p>
      </div>
      
      <el-row :gutter="30">
        <el-col :span="12">
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span>邮箱：<EMAIL></span>
            </div>
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>电话：************</span>
            </div>
            <div class="contact-item">
              <el-icon><Location /></el-icon>
              <span>地址：北京市海淀区中关村大街1号</span>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <el-form :model="contactForm" label-width="80px">
            <el-form-item label="姓名">
              <el-input v-model="contactForm.name" />
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="contactForm.email" />
            </el-form-item>
            <el-form-item label="消息">
              <el-input v-model="contactForm.message" type="textarea" :rows="4" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="sendMessage">发送消息</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { 
  DataAnalysis, 
  Edit, 
  Tools, 
  User, 
  Message, 
  Phone, 
  Location 
} from '@element-plus/icons-vue'

export default {
  name: 'About',
  components: {
    DataAnalysis,
    Edit,
    Tools,
    User,
    Message,
    Phone,
    Location
  },
  data() {
    return {
      contactForm: {
        name: '',
        email: '',
        message: ''
      },
      features: [
        {
          id: 1,
          title: '智能文本分析',
          description: '运用AI技术深度分析文本结构、词汇难度、语法复杂度等多个维度，提供精准的难度评估。',
          icon: 'DataAnalysis',
          color: '#409EFF'
        },
        {
          id: 2,
          title: '个性化简化',
          description: '根据学习者水平智能简化复杂文本，保持原意的同时降低阅读难度，提升理解效果。',
          icon: 'Edit',
          color: '#67C23A'
        },
        {
          id: 3,
          title: '教学工具集',
          description: '为教师提供丰富的教学辅助工具，包括题目生成、课件制作、学情分析等功能。',
          icon: 'Tools',
          color: '#E6A23C'
        }
      ],
      teamMembers: [
        {
          id: 1,
          name: '张教授',
          role: '项目负责人',
          description: '教育技术学博士，专注于AI在教育领域的应用研究',
          avatar: null
        },
        {
          id: 2,
          name: '李工程师',
          role: '技术总监',
          description: '资深AI工程师，在自然语言处理领域有丰富经验',
          avatar: null
        },
        {
          id: 3,
          name: '王老师',
          role: '教育顾问',
          description: '一线语文教师，为产品设计提供专业的教育理论指导',
          avatar: null
        },
        {
          id: 4,
          name: '刘设计师',
          role: 'UI/UX设计师',
          description: '专注于教育产品的用户体验设计，让技术更贴近用户',
          avatar: null
        }
      ]
    }
  },
  methods: {
    sendMessage() {
      if (!this.contactForm.name || !this.contactForm.email || !this.contactForm.message) {
        this.$message.warning('请填写完整信息')
        return
      }
      
      this.$message.success('消息发送成功，我们会尽快回复您！')
      this.contactForm = {
        name: '',
        email: '',
        message: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.about {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 20px;
  text-align: center;
  
  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    
    h1 {
      font-size: 48px;
      margin-bottom: 16px;
      font-weight: bold;
    }
    
    .hero-subtitle {
      font-size: 24px;
      margin-bottom: 24px;
      opacity: 0.9;
    }
    
    .hero-description {
      font-size: 18px;
      line-height: 1.6;
      opacity: 0.8;
      margin: 0;
    }
  }
}

.features-section,
.technology-section,
.team-section,
.contact-section {
  padding: 80px 20px;
  
  .section-header {
    text-align: center;
    margin-bottom: 60px;
    
    h2 {
      font-size: 36px;
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
    }
    
    p {
      font-size: 18px;
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }
}

.features-section {
  background-color: var(--el-fill-color-lighter);
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: 100%;
  
  .feature-icon {
    margin-bottom: 24px;
  }
  
  h3 {
    font-size: 24px;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
  }
  
  p {
    color: var(--el-text-color-regular);
    line-height: 1.6;
    margin: 0;
  }
}

.tech-content {
  h3 {
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
    font-size: 20px;
  }
  
  ul {
    margin-bottom: 32px;
    padding-left: 20px;
    
    li {
      color: var(--el-text-color-regular);
      line-height: 1.8;
      margin-bottom: 8px;
    }
  }
}

.tech-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 24px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
  
  .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: var(--el-color-primary);
    margin-bottom: 8px;
  }
  
  .stat-label {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

.team-section {
  background-color: var(--el-fill-color-lighter);
}

.team-card {
  text-align: center;
  padding: 32px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .member-avatar {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background-color: var(--el-fill-color-light);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  h4 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
    font-size: 18px;
  }
  
  .member-role {
    color: var(--el-color-primary);
    font-weight: 500;
    margin-bottom: 12px;
  }
  
  .member-description {
    color: var(--el-text-color-regular);
    line-height: 1.5;
    font-size: 14px;
    margin: 0;
  }
}

.contact-info {
  .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    font-size: 16px;
    color: var(--el-text-color-regular);
    
    .el-icon {
      margin-right: 12px;
      color: var(--el-color-primary);
    }
  }
}
</style>
