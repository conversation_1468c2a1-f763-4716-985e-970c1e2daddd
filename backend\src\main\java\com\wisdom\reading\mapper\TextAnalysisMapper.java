package com.wisdom.reading.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wisdom.reading.entity.TextAnalysis;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文本分析Mapper接口
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Mapper
public interface TextAnalysisMapper extends BaseMapper<TextAnalysis> {

    /**
     * 根据用户ID查找分析记录
     * 
     * @param userId 用户ID
     * @param gradeLevel 分级筛选（可选）
     * @return 分析记录列表
     */
    List<TextAnalysis> findByUserId(@Param("userId") Long userId, @Param("gradeLevel") String gradeLevel);

    /**
     * 根据分级查找分析记录
     * 
     * @param gradeLevel 分级
     * @return 分析记录列表
     */
    @Select("SELECT * FROM text_analysis WHERE grade_level = #{gradeLevel} AND deleted = 0 ORDER BY create_time DESC")
    List<TextAnalysis> findByGradeLevel(@Param("gradeLevel") String gradeLevel);

    /**
     * 根据状态查找分析记录
     * 
     * @param status 状态
     * @return 分析记录列表
     */
    @Select("SELECT * FROM text_analysis WHERE status = #{status} AND deleted = 0 ORDER BY create_time DESC")
    List<TextAnalysis> findByStatus(@Param("status") String status);

    /**
     * 获取用户分析统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    AnalysisStats getUserAnalysisStats(@Param("userId") Long userId);

    /**
     * 获取系统分析统计
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计信息
     */
    SystemAnalysisStats getSystemAnalysisStats(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 搜索分析记录
     * 
     * @param keyword 关键词
     * @param userId 用户ID（可选）
     * @param gradeLevel 分级（可选）
     * @return 搜索结果
     */
    List<TextAnalysis> searchAnalysis(@Param("keyword") String keyword,
                                    @Param("userId") Long userId,
                                    @Param("gradeLevel") String gradeLevel);

    /**
     * 获取相似文本分析
     * 
     * @param gradeLevel 分级
     * @param topicCategory 主题分类
     * @param excludeId 排除的分析ID
     * @param limit 限制数量
     * @return 相似分析列表
     */
    @Select("SELECT * FROM text_analysis " +
            "WHERE grade_level = #{gradeLevel} " +
            "AND topic_category = #{topicCategory} " +
            "AND id != #{excludeId} " +
            "AND deleted = 0 " +
            "ORDER BY confidence DESC " +
            "LIMIT #{limit}")
    List<TextAnalysis> findSimilarAnalysis(@Param("gradeLevel") String gradeLevel,
                                         @Param("topicCategory") String topicCategory,
                                         @Param("excludeId") Long excludeId,
                                         @Param("limit") Integer limit);

    /**
     * 统计分级分布
     * 
     * @param userId 用户ID（可选）
     * @return 分级分布
     */
    List<GradeDistribution> getGradeDistribution(@Param("userId") Long userId);

    /**
     * 分析统计信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class AnalysisStats {
        private Long totalCount;
        private Long todayCount;
        private Long weekCount;
        private Long monthCount;
        private Double avgConfidence;
        private String mostFrequentGrade;
    }

    /**
     * 系统分析统计
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class SystemAnalysisStats {
        private Long totalAnalysis;
        private Long totalUsers;
        private Long todayAnalysis;
        private Double avgProcessingTime;
        private Double successRate;
    }

    /**
     * 分级分布
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class GradeDistribution {
        private String gradeLevel;
        private Long count;
        private Double percentage;
    }
}
