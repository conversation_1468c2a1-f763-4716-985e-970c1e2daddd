<template>
  <div class="error-404">
    <div class="error-container">
      <div class="error-content">
        <div class="error-image">
          <el-icon :size="120" color="#409EFF">
            <Warning />
          </el-icon>
        </div>
        
        <div class="error-info">
          <h1 class="error-code">404</h1>
          <h2 class="error-title">页面不存在</h2>
          <p class="error-description">
            抱歉，您访问的页面不存在或已被删除。
            <br>
            请检查网址是否正确，或返回首页继续浏览。
          </p>
          
          <div class="error-actions">
            <el-button type="primary" size="large" @click="goHome">
              <el-icon><House /></el-icon>
              返回首页
            </el-button>
            <el-button size="large" @click="goBack">
              <el-icon><Back /></el-icon>
              返回上页
            </el-button>
          </div>
        </div>
      </div>
      
      <div class="error-suggestions">
        <h3>您可以尝试：</h3>
        <ul>
          <li>检查网址拼写是否正确</li>
          <li>使用导航菜单浏览其他页面</li>
          <li>使用搜索功能查找内容</li>
          <li>联系我们获取帮助</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { Warning, House, Back } from '@element-plus/icons-vue'

export default {
  name: 'Error404',
  components: {
    Warning,
    House,
    Back
  },
  methods: {
    goHome() {
      this.$router.push('/')
    },
    
    goBack() {
      // 如果有历史记录则返回，否则跳转到首页
      if (window.history.length > 1) {
        this.$router.go(-1)
      } else {
        this.$router.push('/')
      }
    }
  },
  mounted() {
    // 设置页面标题
    document.title = '404 - 页面不存在 | 智慧阅读'
  }
}
</script>

<style lang="scss" scoped>
.error-404 {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.error-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.error-content {
  background: white;
  border-radius: 16px;
  padding: 60px 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  
  @media (max-width: 768px) {
    padding: 40px 20px;
  }
}

.error-image {
  margin-bottom: 30px;
  
  .el-icon {
    opacity: 0.8;
  }
}

.error-code {
  font-size: 72px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin: 0 0 16px 0;
  line-height: 1;
  
  @media (max-width: 768px) {
    font-size: 56px;
  }
}

.error-title {
  font-size: 32px;
  color: var(--el-text-color-primary);
  margin: 0 0 20px 0;
  font-weight: 600;
  
  @media (max-width: 768px) {
    font-size: 24px;
  }
}

.error-description {
  font-size: 16px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin: 0 0 40px 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  
  .el-button {
    min-width: 140px;
    
    .el-icon {
      margin-right: 8px;
    }
  }
}

.error-suggestions {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  text-align: left;
  
  h3 {
    color: var(--el-text-color-primary);
    margin: 0 0 20px 0;
    font-size: 18px;
    text-align: center;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      color: var(--el-text-color-regular);
      padding: 8px 0;
      position: relative;
      padding-left: 24px;
      
      &:before {
        content: '•';
        color: var(--el-color-primary);
        font-weight: bold;
        position: absolute;
        left: 0;
      }
    }
  }
}

// 动画效果
.error-content {
  animation: fadeInUp 0.6s ease-out;
}

.error-suggestions {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
