{"name": "@hapi/joi", "description": "Object schema validation", "version": "15.1.1", "homepage": "https://github.com/hapijs/joi", "repository": "git://github.com/hapijs/joi", "main": "lib/index.js", "keywords": ["schema", "validation"], "dependencies": {"@hapi/address": "2.x.x", "@hapi/bourne": "1.x.x", "@hapi/hoek": "8.x.x", "@hapi/topo": "3.x.x"}, "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "scripts": {"test": "lab -t 100 -a @hapi/code -L", "test-cov-html": "lab -r html -o coverage.html -a @hapi/code"}, "license": "BSD-3-<PERSON><PERSON>"}